# Uncomment the next line to define a global platform for your project
# platform :ios, '9.0'

target 'CryptaVault' do
  # Comment the next line if you don't want to use dynamic frameworks
  use_frameworks!

  # Pods for CryptaVault
  pod 'Argon2Swift'
  pod 'SnapKit', '~> 5.7.0'

  target 'CryptaVaultTests' do
    inherit! :search_paths
    # Pods for testing
  end

  target 'CryptaVaultUITests' do
    # Pods for testing
  end

end
