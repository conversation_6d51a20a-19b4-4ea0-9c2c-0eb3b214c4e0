@article{hellman1980cryptanalytic,
  title={A cryptanalytic time-memory trade-off},
  author={<PERSON><PERSON>, <PERSON>},
  journal={Information Theory, IEEE Transactions on},
  volume={26},
  number={4},
  pages={401--406},
  year={1980},
  publisher={IEEE}
}


@inproceedings{DworkN92,
  author    = {<PERSON> and
               <PERSON><PERSON>},
  title     = {Pricing via Processing or Combatting Junk Mail},
  booktitle = {CRYPTO'92},
  series    = {Lecture Notes in Computer Science},
  volume    = {740},
  pages     = {139--147},
  publisher = {Springer},
  year      = {1992},
  timestamp = {Fri, 18 Sep 2009 10:18:29 +0200},
  biburl    = {http://dblp.uni-trier.de/rec/bib/conf/crypto/DworkN92},
  bibsource = {dblp computer science bibliography, http://dblp.org}
}

@article{Sudan97,
  author    = {Madhu Sudan},
  title     = {Decoding of {<PERSON>} Codes beyond the Error-Correction Bound},
  journal   = {<PERSON><PERSON>},
  volume    = {13},
  number    = {1},
  pages     = {180--193},
  year      = {1997},
  url       = {http://dx.doi.org/10.1006/jcom.1997.0439},
  doi       = {10.1006/jcom.1997.0439},
  timestamp = {Thu, 10 Nov 2005 11:26:57 +0100},
  biburl    = {http://dblp.uni-trier.de/rec/bib/journals/jc/Sudan97},
  bibsource = {dblp computer science bibliography, http://dblp.org}
}

@article{OorschotW99,
  author    = {Paul C. van Oorschot and
               Michael J. Wiener},
  title     = {Parallel Collision Search with Cryptanalytic Applications},
  journal   = {J. Cryptology},
  volume    = {12},
  number    = {1},
  pages     = {1--28},
  year      = {1999},
  url       = {http://dx.doi.org/10.1007/PL00003816},
  doi       = {10.1007/PL00003816},
  timestamp = {Tue, 24 May 2011 14:18:06 +0200},
  biburl    = {http://dblp.uni-trier.de/rec/bib/journals/joc/OorschotW99},
  bibsource = {dblp computer science bibliography, http://dblp.org}
}

@inproceedings{JakobssonJ99,
  author    = {Markus Jakobsson and
               Ari Juels},
  editor    = {Bart Preneel},
  title     = {Proofs of Work and Bread Pudding Protocols},
  booktitle = {Secure Information Networks: Communications and Multimedia Security,
               {IFIP} {TC6/TC11} Joint Working Conference on Communications and Multimedia
               Security {(CMS} '99), September 20-21, 1999, Leuven, Belgium},
  series    = {{IFIP} Conference Proceedings},
  volume    = {152},
  pages     = {258--272},
  publisher = {Kluwer},
  year      = {1999},
  timestamp = {Mon, 14 Oct 2002 12:00:15 +0200},
  biburl    = {http://dblp.uni-trier.de/rec/bib/conf/cms/JakobssonJ99},
  bibsource = {dblp computer science bibliography, http://dblp.org}
}



@MANUAL{FIPS-197,
TITLE = {{FIPS}-197: {Advanced Encryption Standard}},
organization = {{N}ational {I}nstitute of {S}tandards and {T}echnology ({NIST}), available at \url{http://csrc.nist.gov/publications/fips/fips197/fips-197.pdf}},
month = {November},
year = {2001},
}



@BOOK{DR02,
AUTHOR = {Joan Daemen and Vincent Rijmen},
TITLE = {The Design of {Rijndael}. {AES}~--- the {Advanced Encryption Standard}},
PUBLISHER = {Springer},
YEAR = {2002}
}


@misc{back2002hashcash,
  title={Hashcash -- a denial of service counter-measure},
  author={Back, Adam},
  year={2002},
  note ={available at \url{http://www.hashcash.org/papers/hashcash.pdf}}
}


@inproceedings{DworkGN03,
  author    = {Cynthia Dwork and
               Andrew Goldberg and
               Moni Naor},
  title     = {On Memory-Bound Functions for Fighting Spam},
  booktitle = {CRYPTO'03},
  year      = {2003},
  pages     = {426--444},
  series    = {Lecture Notes in Computer Science},
  volume    = {2729},
  publisher = {Springer}
}


@MANUAL{sha3,
  author = {NIST},
  title =        {SHA-3 competition},
  year =         {2007},
  note =         {\url{http://csrc.nist.gov/groups/ST/hash/sha-3/index.html‎}}
}

@book{robshaw2008new,
  title={New stream cipher designs: the eSTREAM finalists},
  author={Robshaw, Matthew and Billet, Olivier},
  volume={4986},
  year={2008},
  publisher={Springer}
}



@misc{percival2009stronger,
  title={Stronger key derivation via sequential memory-hard functions},
  author={Percival, Colin},
  note={\url{http://www.tarsnap.com/scrypt/scrypt.pdf}},
  year={2009}
}


@misc{litecoin,
  title =        {Litecoin - Open source P2P digital currency},
  author = {Charles Lee},
  year =         {2011},
  note =         {\url{https://litecoin.org/‎}},
  howpublished = {\url{https://bitcointalk.org/index.php?topic=47417.0}}
}



@MANUAL{ietf-scrypt,
  title =        {IETF Draft: The scrypt Password-Based Key Derivation Function},
  year =         {2012},
  note =         {\url{
https://tools.ietf.org/html/draft-josefsson-scrypt-kdf-02}}
}


@MISC{story,
 year=2012,
  title =        {Password security: past, present, future},
  note =         {\url{http://www.openwall.com/presentations/Passwords12-The-Future-Of-Hashing/}}
  }
  
  @article{DziembowskiFKP13,
  author    = {Stefan Dziembowski and
               Sebastian Faust and
               Vladimir Kolmogorov and
               Krzysztof Pietrzak},
  title     = {Proofs of Space},
  journal   = {IACR Cryptology ePrint Archive 2013/796},
  note = {to appear at Crypto'15}
}

@MISC{momentum,
  year = {2013},
  title =        {Momentum: a memory-hard proof-of-work},
  note =         {\url{http://www.hashcash.org/papers/momentum.pdf}}
}


@MISC{ebay,
  year = {2014},
  title =        {{eBay} hacked, requests all users change passwords},
  note =         {\url{http://www.cnet.com/news/ebay-hacked-requests-all-users-change-passwords/}}
}

@TECHREPORT{yescrypt,
  author =       {Alexander Peslyak },
  title =        {Yescrypt - a Password Hashing Competition submission},
  year =         {2014},
  note =         {available at \url{https://password-hashing.net/submissions/specs/yescrypt-v0.pdf}}
}





  
@MISC{bitasic,
  title =        {Avalon ASIC's 40nm Chip to Bring Hashing Boost for Less Power},
  year = {2014},
  note =         {\url{  http://www.coindesk.com/avalon-asics-40nm-chip-bring-hashing-boost-less-power/}}
}

@MISC{comp,
  title =        {{Password Hashing Competition}},
  year = 2015,
  note =         {\url{https://password-hashing.net/}}
}




@MANUAL{vertcoin,
  title =        {Vertcoin: Lyra2RE reference guide},
  year =         {2014},
  note =         {\url{https://vertcoin.org/downloads/Vertcoin_Lyra2RE_Paper_11292014.pdf}}
}



@MANUAL{FIPS-180-4,
TITLE = {{FIPS}-180-4: {Secure Hash Standard}},
organization = {{N}ational {I}nstitute of {S}tandards and {T}echnology ({NIST})},
note={available at \url{http://csrc.nist.gov/publications/fips/fips180-4/fips-180-4.pdf}},
month = {March},
year = {2012},
}





@article{gueronaes,
  title={AES-GCM software performance on the current high end CPUs as a performance baseline for CAESAR competition},
  author={Gueron, Shay},
  year={2013},
  note={\url{http://2013.diac.cr.yp.to/slides/gueron.pdf}}
}


@inproceedings{norwayTrade,
  author    = {Donghoon Chang and Arpan Jati and Sweta Mishra and Somitra Kumar Sanadhya},
  title     = {Time Memory Tradeoff Analysis of Graphs in Password
Hashing Constructions},
  booktitle = {Preproceedings of PASSWORDS'14},
  year      = {2014},
  pages     = {256-266},
  note={available at \url{http://passwords14.item.ntnu.no/Preproceedings_Passwords14.pdf}}
}


@inproceedings{BogdanovKLTVV11,
  author    = {Andrey Bogdanov and
               Miroslav Knezevic and
               Gregor Leander and
               Deniz Toz and
               Kerem Varici and
               Ingrid Verbauwhede},
  title     = {Spongent: A Lightweight Hash Function},
  booktitle = {CHES'11},
  year      = {2011},
  pages     = {312-325},
  publisher = {Springer},
  series    = {Lecture Notes in Computer Science},
  volume    = {6917}
}

@misc{cryptoeprint:2014:881,
    author = {Christian Forler and Eik List and Stefan Lucks and Jakob Wenzel},
    title = {Overview of the Candidates for the Password Hashing Competition - And Their Resistance Against Garbage-Collector Attacks},
    howpublished = {Cryptology ePrint Archive, Report 2014/881},
    year = {2014},
    note = {\url{http://eprint.iacr.org/}},
}

@TECHREPORT{Daemen13,
  author =       {Joan Daemen},
  title =        {Permutation-based symmetric cryptography
and
{Keccak}},
  institution =  {Ecrypt II, Crypto for 2020 Invited Talk},
  year =         {2013},
  note={\url{https://www.cosic.esat.kuleuven.be/ecrypt/cryptofor2020/slides/KeccakEcryptTenerife.pdf}}
}

@inproceedings{AumassonHMN10,
  author    = {Jean-Philippe Aumasson and
               Luca Henzen and
               Willi Meier and
               Mar\'{\i}a Naya-Plasencia},
  title     = {Quark: A Lightweight Hash},
  booktitle = {CHES'10},
  year      = {2010},
  pages     = {1-15},
  publisher = {Springer},
  series    = {Lecture Notes in Computer Science},
  volume    = {6225},
  note= {\url{https://131002.net/quark/quark_full.pdf}}
}

@inproceedings{knudsen1998analysis,
  title={Analysis methods for (alleged) {RC4}},
  author={Knudsen, Lars R and Meier, Willi and Preneel, Bart and Rijmen, Vincent and Verdoolaege, Sven},
  booktitle={Advances in Cryptology—ASIACRYPT’98},
  pages={327--341},
  year={1998},
  organization={Springer}
}


@report{Keccak-ref,
  author    = {Guido Bertoni and
               Joan Daemen and
               Michael Peeters and
               Gilles Van Assche},
  title     = {The {Keccak} reference, version 3.0},
  year      = {2011},
  note = {\url{http://keccak.noekeon.org/Keccak-reference-3.0.pdf}}
}



@inproceedings{DworkNW05,
  author    = {Cynthia Dwork and
               Moni Naor and
               Hoeteck Wee},
  title     = {Pebbling and Proofs of Work},
  booktitle = {{CRYPTO}'05},
  year      = {2005},
  pages     = {37--54},
  series    = {Lecture Notes in Computer Science},
  volume    = {3621},
  publisher = {Springer}
}

@inproceedings{FiatS86,
  author    = {Amos Fiat and
               Adi Shamir},
  editor    = {Andrew M. Odlyzko},
  title     = {How to Prove Yourself: Practical Solutions to Identification and Signature
               Problems},
  booktitle = {Advances in Cryptology - {CRYPTO} '86, Santa Barbara, California,
               USA, 1986, Proceedings},
  series    = {Lecture Notes in Computer Science},
  volume    = {263},
  pages     = {186--194},
  publisher = {Springer},
  year      = {1986},
  url       = {http://dx.doi.org/10.1007/3-540-47721-7_12},
  doi       = {10.1007/3-540-47721-7_12},
  timestamp = {Fri, 18 Sep 2009 08:01:49 +0200},
  biburl    = {http://dblp.uni-trier.de/rec/bib/conf/crypto/FiatS86},
  bibsource = {dblp computer science bibliography, http://dblp.org}
}


@article{HopcroftPV77,
  author    = {John E. Hopcroft and
               Wolfgang J. Paul and
               Leslie G. Valiant},
  title     = {On Time Versus Space},
  journal   = {J. ACM},
  volume    = {24},
  number    = {2},
  year      = {1977},
  pages     = {332-337},
  ee        = {http://doi.acm.org/10.1145/322003.322015},
  bibsource = {DBLP, http://dblp.uni-trier.de}
}

@article{PaulTC77,
  author    = {Wolfgang J. Paul and
               Robert Endre Tarjan and
               James R. Celoni},
  title     = {Space Bounds for a Game on Graphs},
  journal   = {Mathematical Systems Theory},
  volume    = {10},
  year      = {1977},
  pages     = {239-251},
  ee        = {http://dx.doi.org/10.1007/BF01683275},
  bibsource = {DBLP, http://dblp.uni-trier.de}
}

@article{LengauerT82,
  author    = {Thomas Lengauer and
               Robert Endre Tarjan},
  title     = {Asymptotically tight bounds on time-space trade-offs in
               a pebble game},
  journal   = {J. ACM},
  volume    = {29},
  number    = {4},
  year      = {1982},
  pages     = {1087-1130},
  ee        = {http://doi.acm.org/10.1145/322344.322354},
  bibsource = {DBLP, http://dblp.uni-trier.de}
}

@article{AlwenS14,
  author    = {Jo{\"e}l Alwen and
               Vladimir Serbinenko},
  title     = {High Parallel Complexity Graphs and Memory-Hard Functions},
  journal   = {IACR Cryptology ePrint Archive 2014/238}
}

@TECHREPORT{Bernstein05,
  author =       {Daniel J. Bernstein},
  title =        {Cache-timing
attacks
on
AES},
  year =         {2005},
  note =         {\url{http://cr.yp.to/antiforgery/cachetiming-20050414.pdf}}
}

@inproceedings{trade-att,
    author    = {Alex Biryukov and
               Dmitry Khovratovich},
  editor    = {Tetsu Iwata and
               Jung Hee Cheon},
  title     = {Tradeoff Cryptanalysis of Memory-Hard Functions},
  booktitle = {Advances in Cryptology - {ASIACRYPT} 2015 },
  series    = {Lecture Notes in Computer Science},
  volume    = {9453},
  pages     = {633--657},
  publisher = {Springer},
  year      = {2015},
}

@TECHREPORT{Argon2,
  author =       {Alex Biryukov and Daniel Dinu and Dmitry Khovratovich},
  title =        {Argon2},
  year =         {2015},
  note =         {\url{https://www.cryptolux.org/images/0/0d/Argon2.pdf}}
}

@MISC{BSTY,
  title =        {GlobalBoost announces a yescrypt-based cryptocurrency},
  note =         {\url{https://bitcointalk.org/index.php?topic=775289.0}}
}

@article{ForlerLW13,
  author    = {Christian Forler and
               Stefan Lucks and
               Jakob Wenzel},
  title     = {Catena: A Memory-Consuming Password Scrambler},
  journal   = {IACR Cryptology ePrint Archive, Report 2013/525},
  year = {2013},
  note = {non-tweaked version \url{http://eprint.iacr.org/2013/525/20140105:194859}}
}

@misc{broz15,
year = 2015,
author = {Milan Broz},
title = {PHC benchmarks},
note = {\url{https://github.com/mbroz/PHCtest/blob/master/output/phc\_round2.pdf}}
}

 @inproceedings{ForlerLW14,
  author    = {Christian Forler and
               Stefan Lucks and
               Jakob Wenzel},
  title     = {Memory-Demanding Password Scrambling},
  booktitle = {{ASIACRYPT}'14},
   series    = {Lecture Notes in Computer Science},
  volume    = {8874},
  pages     = {289--305},
  publisher = {Springer},
  year      = {2014},
  note = {tweaked version of \cite{ForlerLW13}}
}

@article{ParkPAFG15,
  author    = {Sunoo Park and
               Krzysztof Pietrzak and
               Jo{\"{e}}l Alwen and
               Georg Fuchsbauer and
               Peter Gazi},
  title     = {Spacecoin: {A} Cryptocurrency Based on Proofs of Space},
  journal   = {{IACR} Cryptology ePrint Archive},
  volume    = {2015},
  pages     = {528},
  year      = {2015},
  url       = {http://eprint.iacr.org/2015/528},
  timestamp = {Fri, 26 Jun 2015 09:49:58 +0200},
  biburl    = {http://dblp.uni-trier.de/rec/bib/journals/iacr/ParkPAFG15},
  bibsource = {dblp computer science bibliography, http://dblp.org}
}





@inproceedings{BiryukovS01,
author = {Alex Biryukov and
Adi Shamir},
title = {Structural Cryptanalysis of {SASAS}},
booktitle = {EUROCRYPT'01},
year = {2001}
}

@inproceedings{RistenpartTSS09,
  author    = {Thomas Ristenpart and
               Eran Tromer and
               Hovav Shacham and
               Stefan Savage},
  title     = {Hey, you, get off of my cloud: exploring information leakage in third-party
               compute clouds},
  booktitle = {ACM {CCS}'09},
  year      = {2009},
  pages     = {199--212}
}

@MISC{bitcoin,
  title =        {Bitcoin: Mining hardware comparison},
  year={2014},
  note =         {available at \url{https://en.bitcoin.it/wiki/Mining_hardware_comparison}. We compare $2^{32}$ hashes per joule on the best ASICs with $2^{17}$ hashes per joule on the most efficient x86-laptops.}
}


@MISC{litecoin-comp,
  title =        {Litecoin: Mining hardware comparison},
  note =         {\url{https://litecoin.info/Mining_hardware_comparison}}
}


@article{AbadiBMW05,
  author    = {Mart{\'{\i}}n Abadi and
               Michael Burrows and
               Mark S. Manasse and
               Ted Wobber},
  title     = {Moderately hard, memory-bound functions},
  journal   = {{ACM} Trans. Internet Techn.},
  year      = {2005},
  volume    = {5},
  number    = {2},
  pages     = {299--327},
  url       = {http://doi.acm.org/10.1145/1064340.1064341},
  doi       = {10.1145/1064340.1064341},
  timestamp = {Tue, 09 Sep 2014 16:27:47 +0200},
  biburl    = {http://dblp.uni-trier.de/rec/bib/journals/toit/AbadiBMW05},
  bibsource = {dblp computer science bibliography, http://dblp.org}
}

@article{Pippenger77,
  author    = {Nicholas Pippenger},
  title     = {Superconcentrators},
  journal   = {{SIAM} J. Comput.},
  year      = {1977},
  volume    = {6},
  number    = {2},
  pages     = {298--304},
  url       = {http://dx.doi.org/10.1137/0206022},
  doi       = {10.1137/0206022},
  timestamp = {Tue, 09 Sep 2014 16:52:40 +0200},
  biburl    = {http://dblp.uni-trier.de/rec/bib/journals/siamcomp/Pippenger77},
  bibsource = {dblp computer science bibliography, http://dblp.org}
}

@TECHREPORT{lyra,
  author =       {Marcos A. Simplicio Jr and Leonardo C. Almeida and Ewerton R. Andrade and Paulo C. F. dos Santos and Paulo S. L. M. Barreto},
  title =        {The {Lyra2} reference guide, version 2.3.2},
  year =         {2014},
  month = {april},
  note =         {available at  \url{http://lyra-kdf.net/Lyra2ReferenceGuide_v1.pdf}},
}


@inproceedings{Thompson79,
  author    = {Clark D. Thompson},
  title     = {Area-Time Complexity for {VLSI}},
  booktitle = {STOC'79},
  pages     = {81--88},
  year      = {1979},
  publisher = {{ACM}}
}

@TECHREPORT{pomelo,
  author =       {Hongjun Wu},
  title =        {{POMELO}:
A Password Hashing Algorithm},
  year =         {2014},
  note =         {available at \url{https://password-hashing.net/submissions/specs/POMELO-v1.pdf}},
}


@inproceedings{knudsen1998analysis,
  title={Analysis methods for (alleged) {RC4}},
  author={Knudsen, Lars R and Meier, Willi and Preneel, Bart and Rijmen, Vincent and Verdoolaege, Sven},
  booktitle={Advances in Cryptology—ASIACRYPT’98},
  pages={327--341},
  year={1998},
  organization={Springer}
}

@MISC{fpga,
  title =        {Energy-efficient bcrypt cracking},
author={Katja Malvoni},
  note =         {Passwords'14 conference, available at \url{http://www.openwall.com/presentations/Passwords14-Energy-Efficient-Cracking/}}
}


@MISC{ripper,
  title =        {Software tool: {John the Ripper} password cracker},
  note =         {\url{http://www.openwall.com/john/}}
}

@MISC{sharcs,
title = {{SHARCS} -- Special-purpose Hardware for Attacking Cryptographic Systems},
note = {\url{http://www.sharcs.org/}}
}

@article{Wiener04,
  author    = {Michael J. Wiener},
  title     = {The Full Cost of Cryptanalytic Attacks},
  journal   = {J. Cryptology},
  year      = {2004},
  volume    = {17},
  number    = {2},
  pages     = {105--124},
  url       = {http://dx.doi.org/10.1007/s00145-003-0213-5},
  doi       = {10.1007/s00145-003-0213-5},
  timestamp = {Sat, 27 Sep 2014 18:00:09 +0200},
  biburl    = {http://dblp.uni-trier.de/rec/bib/journals/joc/Wiener04},
  bibsource = {dblp computer science bibliography, http://dblp.org}
}





@inproceedings{MukhopadhyayS06,
  author    = {Sourav Mukhopadhyay and
               Palash Sarkar},
  title     = {On the Effectiveness of {TMTO} and Exhaustive Search Attacks},
  booktitle = {{IWSEC} 2006},
  year      = {2006},
  pages     = {337--352},
  series    = {Lecture Notes in Computer Science},
  volume    = {4266},
  publisher = {Springer}
}



@inproceedings{SprengerB12,
  author    = {Martijn Sprengers and Lejla Batina},
  title     = {Speeding up {GPU-based} password cracking},
  booktitle   = {SHARCS'12},
  year      = {2012},
  note = {available at \url{http://2012.sharcs.org/record.pdf}}
}

@article{nakamoto2012bitcoin,
  title={Bitcoin: A peer-to-peer electronic cash system},
  author={Nakamoto, Satoshi},
  note={\url{http://www. bitcoin.org/bitcoin.pdf}},
  year={2009}
}



@inproceedings{BernsteinL13,
  author    = {Daniel J. Bernstein and
               Tanja Lange},
  title     = {Non-uniform Cracks in the Concrete: The Power of Free Precomputation},
  booktitle = {ASIACRYPT'13},
  year      = {2013},
  pages     = {321--340},
  series    = {Lecture Notes in Computer Science},
  volume    = {8270},
  publisher = {Springer}
}



@inproceedings{AumassonNWW13,
  author    = {Jean{-}Philippe Aumasson and
               Samuel Neves and
               Zooko Wilcox{-}O'Hearn and
               Christian Winnerlein},
  title     = {{BLAKE2:} Simpler, Smaller, Fast as {MD5}},
  booktitle = {{ACNS}'13},
  pages     = {119--135},
  series    = {Lecture Notes in Computer Science},
  year      = {2013},
  volume    = {7954},
  publisher = {Springer}
}


@article{liu2013parallel,
  author    = {Bin Liu and Bevan M. Baas},
  title     = {Parallel {AES} Encryption Engines for Many-Core Processor Arrays},
  journal   = {{IEEE} Transactions on Computers},
  year      = {2013},
  volume    = {62},
  number    = {3},
  pages     = {536--547},
  month     = mar,
}

@article{ForlerLLW14,
  author    = {Christian Forler and
               Eik List and
               Stefan Lucks and
               Jakob Wenzel},
  title     = {Overview of the Candidates for the Password Hashing Competition -
               And their Resistance against Garbage-Collector Attacks},
  journal   = {{IACR} Cryptology ePrint Archive},
  volume    = {2014},
  pages     = {881},
  year      = {2014},
  url       = {http://eprint.iacr.org/2014/881},
  timestamp = {Sat, 02 Mar 4439591 14:05:04 +},
  biburl    = {http://dblp.uni-trier.de/rec/bib/journals/iacr/ForlerLLW14},
  bibsource = {dblp computer science bibliography, http://dblp.org}
}

@inproceedings{gurkaynak2012sha3,
author = {Frank G{\"{u}}rkaynak and Kris Gaj and Beat Muheim and Ekawat Homsirikamol and Christoph Keller and Marcin Rogawski and Hubert Kaeslin and Jens-Peter Kaps},
title = {Lessons Learned from Designing a 65nm {ASIC} for Evaluating Third Round {SHA-3} Candidates},
booktitle = {Third SHA-3 Candidate Conference},
month = mar,
year = {2012}
}

@inproceedings{giridhar2013dram,
  author    = {Bharan Giridhar and Michael Cieslak and Deepankar Duggal and Ronald G. Dreslinski and Hsing Min Chen and Robert Patti and Betina Hold and Chaitali Chakrabarti and Trevor N. Mudge and David Blaauw},
  title     = {Exploring {DRAM} organizations for energy-efficient and resilient
               exascale memories},
  booktitle = {International Conference for High Performance Computing, Networking,
               Storage and Analysis (SC 2013)},
  year      = {2013},
  pages     = {23--35},
  publisher = {ACM},
}

@inproceedings{BertoniDPA11,
  author    = {Guido Bertoni and
               Joan Daemen and
               Michael Peeters and
               Gilles Van Assche},
  title     = {Duplexing the Sponge: Single-Pass Authenticated Encryption and Other
               Applications},
  booktitle = {{SAC}'11,},
  series    = {Lecture Notes in Computer Science},
  volume    = {7118},
  pages     = {320--337},
  publisher = {Springer},
  year      = {2011}
}

@inproceedings{Rig,
  author    = {Donghoon Chang and Arpan Jati and Sweta Mishra and Somitra Sanadhya},
  title     = {Rig: A simple, secure and flexible design for Password Hashing},
  booktitle = {Inscrypt'14},
  series    = {Lecture Notes in Computer Science, to appear},
  publisher = {Springer},
  year      = {2014}
}

@article{BiryukovP14,
  author    = {Alex Biryukov and
               Ivan Pustogarov},
  title     = {Proof-of-Work as Anonymous Micropayment: Rewarding a {Tor} Relay},
  journal   = {{IACR} Cryptology ePrint Archive 2014/1011},
  note= {to appear at Financial Cryptography 2015},
  url       = {http://eprint.iacr.org/2014/1011},
  timestamp = {Mon, 19 Jan 2015 11:11:51 +0100},
  biburl    = {http://dblp.uni-trier.de/rec/bib/journals/iacr/BiryukovP14},
  bibsource = {dblp computer science bibliography, http://dblp.org}
}


@misc{Andersen14,
    author = {David Andersen},
    title = {A Public Review of Cuckoo Cycle},
    howpublished = {\url{http://www.cs.cmu.edu/~dga/crypto/cuckoo/analysis.pdf}},
    year = {2014}
}

@misc{Tromp14,
    author = {John Tromp},
    title = {Cuckoo Cycle: a memory bound graph-theoretic proof-of-work},
    howpublished = {Cryptology ePrint Archive, Report 2014/059},
    year = {2014},
    note = {\url{http://eprint.iacr.org/2014/059}, project webpage \url{https://github.com/tromp/cuckoo}},
}

@misc{cryptoeprint:2015:136,
    author = {Marcos A. Simplicio Jr. and Leonardo C. Almeida and Ewerton R. Andrade and Paulo C. F. dos Santos and Paulo S. L. M. Barreto},
    title = {Lyra2: Password Hashing Scheme with improved security against time-memory trade-offs},
    howpublished = {Cryptology ePrint Archive, Report 2015/136},
    year = {2015},
    note = {\url{http://eprint.iacr.org/}},
}

@article{Corrigan-GibbsB16,
  author    = {Henry Corrigan{-}Gibbs and
               Dan Boneh and
               Stuart E. Schechter},
  title     = {Balloon Hashing: Provably Space-Hard Hash Functions with Data-Independent
               Access Patterns},
  journal   = {{IACR} Cryptology ePrint Archive},
  volume    = {2016},
  pages     = {27},
  year      = {2016}
}


@article{AB16,
  author    = {Joel Alwen and Jeremiah Blocki},
  title     = {Efficiently Computing Data-Independent Memory-Hard Functions},
  journal   = {{IACR} Cryptology ePrint Archive},
  volume    = {2016},
  pages     = {115},
  year      = {2016}
}