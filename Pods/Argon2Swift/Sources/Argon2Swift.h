//
//  Argon2Swift_macOS.h
//  Argon2Swift macOS
//
//  Created by <PERSON><PERSON> on 1/15/21.
//

#import <Foundation/Foundation.h>
#import <Argon2Swift/Argon2Swift.h>
//! Project version number for Argon2Swift_macOS.
FOUNDATION_EXPORT double Argon2SwiftVersionNumber;

//! Project version string for Argon2Swift_macOS.
FOUNDATION_EXPORT const unsigned char Argon2SwiftVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <Argon2Swift_macOS/PublicHeader.h>


