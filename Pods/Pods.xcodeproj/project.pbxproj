// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		064D909CD827405E8DCC309DB1B7775A /* ConstraintLayoutSupportDSL.swift in Sources */ = {isa = PBXBuildFile; fileRef = F76C5F03B87F011F19F1FE46DDD6818D /* ConstraintLayoutSupportDSL.swift */; };
		09E1F569A93FAD4B9149E30B9301F44A /* ConstraintPriority.swift in Sources */ = {isa = PBXBuildFile; fileRef = A52FF97F798EF9B4BBCE4E24CAD3F94D /* ConstraintPriority.swift */; };
		0CA7A132ABE7018DE9295456732F38BB /* ConstraintAttributes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08BE246A0E2CBCD9BD871C459DAAF292 /* ConstraintAttributes.swift */; };
		0DE5DB9C6227B3416778D8417DD95EA9 /* ConstraintView+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 520BD0F9D17A462E166B268FB4B63131 /* ConstraintView+Extensions.swift */; };
		1194E62AA3F6F506799B1A43B16942B5 /* ConstraintDirectionalInsets.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B9ABDB98908EC79254A977815A1F5ED /* ConstraintDirectionalInsets.swift */; };
		205EB01AED14BB574DD54EAFE26E4786 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */; };
		207AA154418043D750A07C948AC9002E /* blamka-round-ref.h in Headers */ = {isa = PBXBuildFile; fileRef = 2AAB2A23226B4DA8488E4C90F7FE8EA4 /* blamka-round-ref.h */; settings = {ATTRIBUTES = (Project, ); }; };
		298FC24E88A3A8F83707378E591601DF /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */; };
		2B2EB369550CE92CEEFCBFD3D32B8A3F /* ConstraintInsetTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5AFBC65BA58C17AB0213178CFB5B8A85 /* ConstraintInsetTarget.swift */; };
		2F6FAD6E0909054DC5509040333F2B54 /* ref.c in Sources */ = {isa = PBXBuildFile; fileRef = 153DF080F266F1D4DF76D2775C4D723A /* ref.c */; };
		33474669ECB03956E9E4C8EAA979BD02 /* Argon2Swift.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1E7538A7860BC98782C35FB798CB77B3 /* Argon2Swift.swift */; };
		3577F172FA68CBAE47CFEE6FE25C5404 /* ConstraintOffsetTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 36B82DEA0C847797A50F98EA463EECD0 /* ConstraintOffsetTarget.swift */; };
		3D3B646B4988314275B40E97BEB16C7F /* ConstraintLayoutGuideDSL.swift in Sources */ = {isa = PBXBuildFile; fileRef = 55CAA01897701BABF1C9A2EFD3FEC97A /* ConstraintLayoutGuideDSL.swift */; };
		45A7A7EA8B7E44BACC20772EDAED35BE /* thread.c in Sources */ = {isa = PBXBuildFile; fileRef = E552C907BB0F9CBAED8A5BAD540D8A3D /* thread.c */; };
		4E8B41AFBEE914ABD496768D44ABA63D /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */; };
		4F4DEB687C0E4834A5B291DEE0651D6A /* ConstraintMaker.swift in Sources */ = {isa = PBXBuildFile; fileRef = 07FB49216259A1FFDA7DF2CA5A8CF705 /* ConstraintMaker.swift */; };
		57C4F6EFB30DDD14E960AC2D6B34F904 /* SnapKit-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = DE3F559F3049A591BC9A3FE1837F1BAD /* SnapKit-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5922A6A0AE7152CF436356B3556F1835 /* ConstraintItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = B0138473A3C3BB1B89DC53CCBF893898 /* ConstraintItem.swift */; };
		59F34874DA4ABB2F5C4E09EA6865936B /* ConstraintLayoutGuide.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B97DCAB42EA7E826A4C5C86BEC41A99 /* ConstraintLayoutGuide.swift */; };
		608016C976D0B713FADDFA2E690B0E91 /* Argon2SwiftException.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF99469FA99F5A55CF46B489B39F2B0E /* Argon2SwiftException.swift */; };
		6E39129FC8643A70C276801FEF4C280D /* Constraint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 90136A13465C8B2C8FD609E0A4C92243 /* Constraint.swift */; };
		70E64058741152093089E1B84042415C /* Salt.swift in Sources */ = {isa = PBXBuildFile; fileRef = 61B94E2E0DB8CF61478E5572CB30FD9D /* Salt.swift */; };
		74187130EE6FF609966EEB7D523F43D2 /* argon2.h in Headers */ = {isa = PBXBuildFile; fileRef = 98617B298513359CFB98D102BD6C1E1B /* argon2.h */; settings = {ATTRIBUTES = (Private, ); }; };
		7521665F6055A7A0D4346F82AC4F0AA6 /* core.c in Sources */ = {isa = PBXBuildFile; fileRef = 427A5872C6837F9C7137E70E359C18B7 /* core.c */; };
		7AF516B98D45391B909D507D0244104C /* ConstraintDescription.swift in Sources */ = {isa = PBXBuildFile; fileRef = F7D127F96A6581F289570D7C11168A9F /* ConstraintDescription.swift */; };
		7BCA0D2B5DFFAB46ECC4A3C19B23A849 /* Pods-CryptaVault-CryptaVaultUITests-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = A0E4983A10372DA35F8757E613A25F27 /* Pods-CryptaVault-CryptaVaultUITests-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7D42390CDB4FA147504B03DA2A174A0C /* ConstraintViewDSL.swift in Sources */ = {isa = PBXBuildFile; fileRef = B4F453180D034592A4879B4A8EFFBBF1 /* ConstraintViewDSL.swift */; };
		868A9F524A7985BDA1EA124D9BF4CA63 /* ConstraintDSL.swift in Sources */ = {isa = PBXBuildFile; fileRef = 842C2E7D8592C455CD8D6C78B6BD5838 /* ConstraintDSL.swift */; };
		86CAB01D950C8BC35EDE0BDC01A2500B /* ConstraintView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1514F2874E58B7F7CF7598A4677EE91E /* ConstraintView.swift */; };
		86F500C9ED0D1869F16AE06A029D4503 /* Pods-CryptaVault-CryptaVaultUITests-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = C1975ABA651BD4AD225091841EA3E13D /* Pods-CryptaVault-CryptaVaultUITests-dummy.m */; };
		883EDEE1C699497CF2A77C3B8A32A790 /* ConstraintMultiplierTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9B5A50F8B7C2B95EC18CEA5D3B17FDE0 /* ConstraintMultiplierTarget.swift */; };
		88A81F31EE132CEEC2BF09D8822DB822 /* blake2b.c in Sources */ = {isa = PBXBuildFile; fileRef = 452B260A9CD073FF7E21470E9D4B2858 /* blake2b.c */; };
		8BABA32F7B94A25D8E9208C0A8D90B2E /* ConstraintMakerRelatable+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = B110DBC28F336D822253782B118E223C /* ConstraintMakerRelatable+Extensions.swift */; };
		90425D2F6C13B6734CEFB86A44FDB8FF /* blake2.h in Headers */ = {isa = PBXBuildFile; fileRef = 8ECB03FB14CB7FE5A624465E7095668D /* blake2.h */; settings = {ATTRIBUTES = (Project, ); }; };
		956C47C4AAA836574B5D752DA4C12465 /* core.h in Headers */ = {isa = PBXBuildFile; fileRef = E669B9FBDDC3A15261E98039724F0907 /* core.h */; settings = {ATTRIBUTES = (Private, ); }; };
		9945723039A7F6833A43776DAF377029 /* blake2-impl.h in Headers */ = {isa = PBXBuildFile; fileRef = AAD857F5C0141C816E8FCC4C028D505A /* blake2-impl.h */; settings = {ATTRIBUTES = (Project, ); }; };
		9A587F4A4C9A23A694A617BE1466296F /* argon2.c in Sources */ = {isa = PBXBuildFile; fileRef = 82737147DDE2C6E0440BA31FF0FF952E /* argon2.c */; };
		9E0045B41BFE697DB4ADE151228024D2 /* SnapKit-SnapKit_Privacy in Resources */ = {isa = PBXBuildFile; fileRef = B9DCB5EC0B1CDADD221717CADDF62359 /* SnapKit-SnapKit_Privacy */; };
		A26010B4AC30AB908102417666F48F06 /* Argon2Swift-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = C842E2C85183801B11A259FF24FE5D24 /* Argon2Swift-dummy.m */; };
		A4B5EA679FE30AD3ADFCA3AC7D40792A /* Pods-CryptaVaultTests-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = C8BDCC8D1D8A26E908B67C7E15698C64 /* Pods-CryptaVaultTests-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A52CB3844E4EF94A6142533B70BE5FF6 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */; };
		A5C163CD0CB36508E81C563C9AB00F9B /* encoding.h in Headers */ = {isa = PBXBuildFile; fileRef = FE39C839EF05DE33B2B2E8D41430B716 /* encoding.h */; settings = {ATTRIBUTES = (Private, ); }; };
		A74C779C5D28AE54CAD43F0C13EC0B38 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */; };
		A7B6F9C2AC29BCCA0FE9FAE4808BB84D /* encoding.c in Sources */ = {isa = PBXBuildFile; fileRef = 9A999AD41CC2D8E43A24C54EE3F67190 /* encoding.c */; };
		A7BE54E5F8A96F3D0E21B79F7E0DB365 /* Argon2SwiftResult.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1C978B2816D33A4D5C19566C0A986067 /* Argon2SwiftResult.swift */; };
		AABEF13464BA7F4621BD94736C1D057C /* ConstraintMakerPrioritizable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3CE68878159D0F21EF4BEBE070329E84 /* ConstraintMakerPrioritizable.swift */; };
		AE224EDB6D044C0FE86B086E950FC2F9 /* Debugging.swift in Sources */ = {isa = PBXBuildFile; fileRef = 768706019BAC90E03D429C77573B5D3E /* Debugging.swift */; };
		AF760C78F1C7E11BF7CB9E9B29903530 /* ConstraintInsets.swift in Sources */ = {isa = PBXBuildFile; fileRef = B0224320DFA022DFA0076D876ED1766D /* ConstraintInsets.swift */; };
		B0875E3AB8718E7DFE5C53497C02A15E /* ConstraintLayoutSupport.swift in Sources */ = {isa = PBXBuildFile; fileRef = D36DF5E35313F8C69F6E604696C6468A /* ConstraintLayoutSupport.swift */; };
		B4BB4F9DDDBD927B7B0F54F903FE3447 /* Pods-CryptaVaultTests-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = ED76BBEA13245638D8ED4DAE76E93683 /* Pods-CryptaVaultTests-dummy.m */; };
		B903049E7C1BED7918DAB208754107C7 /* ConstraintMakerFinalizable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E99F3B6A1E1071607EF8FA5EF6B1BB4 /* ConstraintMakerFinalizable.swift */; };
		BA2FB695DEB0D179253EEB8DFCE3578B /* SnapKit-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 70BB8D3CB593E65CA964AC97601A404C /* SnapKit-dummy.m */; };
		BDA5C7CC91E86448237CF40954FAC5AF /* ConstraintMakerRelatable.swift in Sources */ = {isa = PBXBuildFile; fileRef = F5FC9CF5D16536799DB80F1A32E0B9BC /* ConstraintMakerRelatable.swift */; };
		BF1AE4D97E813B95C43EA4A298B973D1 /* LayoutConstraint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0A63CCBDFA5CC0AC259990C95445C689 /* LayoutConstraint.swift */; };
		C07CB3E9A4D1BF00F841E4285629A2B2 /* ConstraintRelatableTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = AE6E681FD8389C11AE14878FA62016FE /* ConstraintRelatableTarget.swift */; };
		C14F10B663FE2898EACAB90C202B3F50 /* ConstraintMakerEditable.swift in Sources */ = {isa = PBXBuildFile; fileRef = C0D83686B069E2827F429FA728A012AD /* ConstraintMakerEditable.swift */; };
		C2B0677591B11454F8A74199F835954D /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 2C5C54C69A99AA6C35858D267B27DB41 /* PrivacyInfo.xcprivacy */; };
		C35CDAD05F62E7033CF41CD268EB6935 /* thread.h in Headers */ = {isa = PBXBuildFile; fileRef = B49B53A0BC8A4E90F81B4465FE0334AE /* thread.h */; settings = {ATTRIBUTES = (Private, ); }; };
		C6A4302ACE006C4E2CDD481287E2916B /* Typealiases.swift in Sources */ = {isa = PBXBuildFile; fileRef = A3A7E1A489CF2809DE0268C444596293 /* Typealiases.swift */; };
		C6F45595676957ADBEC18EB3F23EAEC4 /* LayoutConstraintItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 44DFE8899400050EA6149BD5DBCAF872 /* LayoutConstraintItem.swift */; };
		CE593943A9E7CF83822CF60304BCAD43 /* ConstraintConstantTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0ED8CD3250B09F2EEC3894A576D19C69 /* ConstraintConstantTarget.swift */; };
		D03404E5BCE900BE79A54461FCAD3DCA /* Argon2Swift-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 8D514D4FD731A4AF585200BD12D959F2 /* Argon2Swift-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D0ECCE69F62EEC4D4CCC56BAEDA03C85 /* Pods-CryptaVault-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = CDEFDC1C28FF21432851B7A17637D7B3 /* Pods-CryptaVault-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D4218DA55B2BA45937589200CC0DF1FB /* ConstraintMakerExtendable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 80FBDCBACD2602EB2621FDE75231F8C4 /* ConstraintMakerExtendable.swift */; };
		D7CC4F81116718D47E0F1B95CD5D188A /* Argon2SwiftErrorCode.swift in Sources */ = {isa = PBXBuildFile; fileRef = FA13DEB23329E77512BC43335CFE51A0 /* Argon2SwiftErrorCode.swift */; };
		DBA4803F4765E1650B8C6841157F5D73 /* ConstraintPriorityTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8AFE4951CDC420D5DBC6B6B8769B6D6F /* ConstraintPriorityTarget.swift */; };
		DD4CF0EC69CDA3BC37D2625E406B0B07 /* Argon2Version.swift in Sources */ = {isa = PBXBuildFile; fileRef = AAFE52443B45C2FAC574F503E334BBE9 /* Argon2Version.swift */; };
		E024C786320E92EF0802680E5C69E60F /* Pods-CryptaVault-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = D41D49B4DEED0B880AF7ADB3191D9338 /* Pods-CryptaVault-dummy.m */; };
		E37671A03B4C17A1CF3766A6125833BB /* ConstraintDirectionalInsetTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 76257782354EA6B77B19E706E7731AE3 /* ConstraintDirectionalInsetTarget.swift */; };
		E3D779DEE753C0B0D33BA8E73A980265 /* ConstraintLayoutGuide+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3DD2097F773A5FDBDD2B6401EA5DA3C6 /* ConstraintLayoutGuide+Extensions.swift */; };
		ECC5C2ADC2682F9171FEA22AF10DCE53 /* ConstraintRelation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E55558B1A9F9C6BAA92F2978EDB0BCF /* ConstraintRelation.swift */; };
		F6F33E8B268F3D41075374D95B8088DC /* UILayoutSupport+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = C9543D1C7BF8471FE46EE844DFBDA902 /* UILayoutSupport+Extensions.swift */; };
		F9EBA65892D78A31C068D727D84BCB88 /* ConstraintConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = B797FA35CA91FC24ED1C161F8B92E257 /* ConstraintConfig.swift */; };
		FCAEF5D2CEEA4BA3AA5D479A4AC37F65 /* Argon2Swift.h in Headers */ = {isa = PBXBuildFile; fileRef = 0B37B0591B212BA543F594A7664564B2 /* Argon2Swift.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FD3EE9637D0DEA764FAFD0DF4A91BF5D /* Argon2Type.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8FCC6C2CFE12C0773B43CDF3F2E3CE54 /* Argon2Type.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		174CAF5500A1DCCCA4BFEC05B1B72476 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F6790A9A99E70846CC3D02929637929E;
			remoteInfo = "Pods-CryptaVault";
		};
		438C379A69C6D0D46118E90DD9783E49 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 774F69F884C9EC391E56B0EF4A748233;
			remoteInfo = Argon2Swift;
		};
		499E2B7A89C793EC8522F937D5FCF2E3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 19622742EBA51E823D6DAE3F8CDBFAD4;
			remoteInfo = SnapKit;
		};
		58481F273575D29189E12ADA6E2FFEF3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 774F69F884C9EC391E56B0EF4A748233;
			remoteInfo = Argon2Swift;
		};
		87B0354C3433F475ABCD7065C356B2A3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 19622742EBA51E823D6DAE3F8CDBFAD4;
			remoteInfo = SnapKit;
		};
		E08D009B4980AAF9419F2D4DB1F177F2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8A8DB685241263AFDF5E6B20FE67B93A;
			remoteInfo = "SnapKit-SnapKit_Privacy";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		07FB49216259A1FFDA7DF2CA5A8CF705 /* ConstraintMaker.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMaker.swift; path = Sources/ConstraintMaker.swift; sourceTree = "<group>"; };
		08BE246A0E2CBCD9BD871C459DAAF292 /* ConstraintAttributes.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintAttributes.swift; path = Sources/ConstraintAttributes.swift; sourceTree = "<group>"; };
		0A63CCBDFA5CC0AC259990C95445C689 /* LayoutConstraint.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LayoutConstraint.swift; path = Sources/LayoutConstraint.swift; sourceTree = "<group>"; };
		0B37B0591B212BA543F594A7664564B2 /* Argon2Swift.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = Argon2Swift.h; path = Sources/Argon2Swift.h; sourceTree = "<group>"; };
		0DA8A12E346CD38D7F746A1C981334A4 /* Pods-CryptaVault.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-CryptaVault.release.xcconfig"; sourceTree = "<group>"; };
		0E99F3B6A1E1071607EF8FA5EF6B1BB4 /* ConstraintMakerFinalizable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMakerFinalizable.swift; path = Sources/ConstraintMakerFinalizable.swift; sourceTree = "<group>"; };
		0ED8CD3250B09F2EEC3894A576D19C69 /* ConstraintConstantTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintConstantTarget.swift; path = Sources/ConstraintConstantTarget.swift; sourceTree = "<group>"; };
		137C9F34CF85DC4B712EFE104B21C1A7 /* SnapKit.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SnapKit.release.xcconfig; sourceTree = "<group>"; };
		14DFB0F840BC64B9AE372FAE7FEAF343 /* Pods-CryptaVault.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-CryptaVault.debug.xcconfig"; sourceTree = "<group>"; };
		1514F2874E58B7F7CF7598A4677EE91E /* ConstraintView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintView.swift; path = Sources/ConstraintView.swift; sourceTree = "<group>"; };
		153DF080F266F1D4DF76D2775C4D723A /* ref.c */ = {isa = PBXFileReference; includeInIndex = 1; name = ref.c; path = Sources/Argon2/src/ref.c; sourceTree = "<group>"; };
		167834A86B509659EEEBE846264B475F /* Pods-CryptaVault-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-CryptaVault-acknowledgements.plist"; sourceTree = "<group>"; };
		19E5B6490CE0B2208824E6C1131D3285 /* Pods-CryptaVaultTests-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-CryptaVaultTests-Info.plist"; sourceTree = "<group>"; };
		1BB047E6DAA9DF532CABF8A256C33B11 /* Pods-CryptaVault */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-CryptaVault"; path = Pods_CryptaVault.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		1C978B2816D33A4D5C19566C0A986067 /* Argon2SwiftResult.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Argon2SwiftResult.swift; path = Sources/Swift/Argon2SwiftResult.swift; sourceTree = "<group>"; };
		1E7538A7860BC98782C35FB798CB77B3 /* Argon2Swift.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Argon2Swift.swift; path = Sources/Swift/Argon2Swift.swift; sourceTree = "<group>"; };
		2059EA97CBEA8FBCDED23B5A48CC0B74 /* Argon2Swift.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = Argon2Swift.modulemap; sourceTree = "<group>"; };
		21D76151EEB30D170EDF38A62E90EC30 /* Pods-CryptaVault-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-CryptaVault-acknowledgements.markdown"; sourceTree = "<group>"; };
		2A4DE497833EBAE67BFF6233F2148974 /* SnapKit-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "SnapKit-prefix.pch"; sourceTree = "<group>"; };
		2A82E00EE9D5683E4D1309E85388B924 /* Pods-CryptaVaultTests */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-CryptaVaultTests"; path = Pods_CryptaVaultTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		2AAB2A23226B4DA8488E4C90F7FE8EA4 /* blamka-round-ref.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "blamka-round-ref.h"; path = "Sources/Argon2/src/blake2/blamka-round-ref.h"; sourceTree = "<group>"; };
		2B97DCAB42EA7E826A4C5C86BEC41A99 /* ConstraintLayoutGuide.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintLayoutGuide.swift; path = Sources/ConstraintLayoutGuide.swift; sourceTree = "<group>"; };
		2B9ABDB98908EC79254A977815A1F5ED /* ConstraintDirectionalInsets.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintDirectionalInsets.swift; path = Sources/ConstraintDirectionalInsets.swift; sourceTree = "<group>"; };
		2C5C54C69A99AA6C35858D267B27DB41 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = Sources/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		2DEFFC159F126EA77BE1BA7860573978 /* Pods-CryptaVault-CryptaVaultUITests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-CryptaVault-CryptaVaultUITests.debug.xcconfig"; sourceTree = "<group>"; };
		328E0F4C0E80F5645384C3845DE31628 /* Argon2Swift-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Argon2Swift-prefix.pch"; sourceTree = "<group>"; };
		36B82DEA0C847797A50F98EA463EECD0 /* ConstraintOffsetTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintOffsetTarget.swift; path = Sources/ConstraintOffsetTarget.swift; sourceTree = "<group>"; };
		384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		3CE68878159D0F21EF4BEBE070329E84 /* ConstraintMakerPrioritizable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMakerPrioritizable.swift; path = Sources/ConstraintMakerPrioritizable.swift; sourceTree = "<group>"; };
		3DD2097F773A5FDBDD2B6401EA5DA3C6 /* ConstraintLayoutGuide+Extensions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "ConstraintLayoutGuide+Extensions.swift"; path = "Sources/ConstraintLayoutGuide+Extensions.swift"; sourceTree = "<group>"; };
		3FA577F88CFBE03044BBEA2C70488812 /* ResourceBundle-SnapKit_Privacy-SnapKit-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-SnapKit_Privacy-SnapKit-Info.plist"; sourceTree = "<group>"; };
		427A5872C6837F9C7137E70E359C18B7 /* core.c */ = {isa = PBXFileReference; includeInIndex = 1; name = core.c; path = Sources/Argon2/src/core.c; sourceTree = "<group>"; };
		44DFE8899400050EA6149BD5DBCAF872 /* LayoutConstraintItem.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LayoutConstraintItem.swift; path = Sources/LayoutConstraintItem.swift; sourceTree = "<group>"; };
		452B260A9CD073FF7E21470E9D4B2858 /* blake2b.c */ = {isa = PBXFileReference; includeInIndex = 1; name = blake2b.c; path = Sources/Argon2/src/blake2/blake2b.c; sourceTree = "<group>"; };
		4C95FFD2D2B46DF0A2DC18A1702F8559 /* SnapKit.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SnapKit.debug.xcconfig; sourceTree = "<group>"; };
		520BD0F9D17A462E166B268FB4B63131 /* ConstraintView+Extensions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "ConstraintView+Extensions.swift"; path = "Sources/ConstraintView+Extensions.swift"; sourceTree = "<group>"; };
		55CAA01897701BABF1C9A2EFD3FEC97A /* ConstraintLayoutGuideDSL.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintLayoutGuideDSL.swift; path = Sources/ConstraintLayoutGuideDSL.swift; sourceTree = "<group>"; };
		57D1B7D5CF49B94E5A372AF2336784DC /* Pods-CryptaVault-CryptaVaultUITests-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-CryptaVault-CryptaVaultUITests-frameworks.sh"; sourceTree = "<group>"; };
		5AFBC65BA58C17AB0213178CFB5B8A85 /* ConstraintInsetTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintInsetTarget.swift; path = Sources/ConstraintInsetTarget.swift; sourceTree = "<group>"; };
		607A7153C0032536D3E0B32A292AA948 /* Pods-CryptaVault.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-CryptaVault.modulemap"; sourceTree = "<group>"; };
		61B94E2E0DB8CF61478E5572CB30FD9D /* Salt.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Salt.swift; path = Sources/Swift/Salt.swift; sourceTree = "<group>"; };
		67EB7E4A6FD14911128B5CE93D2CE7E8 /* Pods-CryptaVaultTests.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-CryptaVaultTests.modulemap"; sourceTree = "<group>"; };
		6B36F0E756245FC6CF88A30B6D40EE02 /* SnapKit.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = SnapKit.modulemap; sourceTree = "<group>"; };
		70BB8D3CB593E65CA964AC97601A404C /* SnapKit-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "SnapKit-dummy.m"; sourceTree = "<group>"; };
		76257782354EA6B77B19E706E7731AE3 /* ConstraintDirectionalInsetTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintDirectionalInsetTarget.swift; path = Sources/ConstraintDirectionalInsetTarget.swift; sourceTree = "<group>"; };
		768706019BAC90E03D429C77573B5D3E /* Debugging.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Debugging.swift; path = Sources/Debugging.swift; sourceTree = "<group>"; };
		7E21F391FD7D0D2F086AACD2A3C4607C /* Pods-CryptaVaultTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-CryptaVaultTests.release.xcconfig"; sourceTree = "<group>"; };
		7E55558B1A9F9C6BAA92F2978EDB0BCF /* ConstraintRelation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintRelation.swift; path = Sources/ConstraintRelation.swift; sourceTree = "<group>"; };
		7EBEAE1FC43E382D5A7EB5CEB147A7BC /* Pods-CryptaVault-CryptaVaultUITests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-CryptaVault-CryptaVaultUITests.release.xcconfig"; sourceTree = "<group>"; };
		8088CFF4BBD27BA472A6B9F8198BF8F1 /* Pods-CryptaVault-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-CryptaVault-Info.plist"; sourceTree = "<group>"; };
		80FBDCBACD2602EB2621FDE75231F8C4 /* ConstraintMakerExtendable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMakerExtendable.swift; path = Sources/ConstraintMakerExtendable.swift; sourceTree = "<group>"; };
		81D44E04F0CB761E8CDB5AFC164084B7 /* Pods-CryptaVaultTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-CryptaVaultTests.debug.xcconfig"; sourceTree = "<group>"; };
		82737147DDE2C6E0440BA31FF0FF952E /* argon2.c */ = {isa = PBXFileReference; includeInIndex = 1; name = argon2.c; path = Sources/Argon2/src/argon2.c; sourceTree = "<group>"; };
		8312D925DCAFBE97AB3C88D555A6430A /* Pods-CryptaVault-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-CryptaVault-frameworks.sh"; sourceTree = "<group>"; };
		842C2E7D8592C455CD8D6C78B6BD5838 /* ConstraintDSL.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintDSL.swift; path = Sources/ConstraintDSL.swift; sourceTree = "<group>"; };
		8AFE4951CDC420D5DBC6B6B8769B6D6F /* ConstraintPriorityTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintPriorityTarget.swift; path = Sources/ConstraintPriorityTarget.swift; sourceTree = "<group>"; };
		8D514D4FD731A4AF585200BD12D959F2 /* Argon2Swift-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Argon2Swift-umbrella.h"; sourceTree = "<group>"; };
		8ECB03FB14CB7FE5A624465E7095668D /* blake2.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = blake2.h; path = Sources/Argon2/src/blake2/blake2.h; sourceTree = "<group>"; };
		8FADF3F4E12F8CC92A603C78F96462D3 /* SnapKit-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "SnapKit-Info.plist"; sourceTree = "<group>"; };
		8FCC6C2CFE12C0773B43CDF3F2E3CE54 /* Argon2Type.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Argon2Type.swift; path = Sources/Swift/Argon2Type.swift; sourceTree = "<group>"; };
		90136A13465C8B2C8FD609E0A4C92243 /* Constraint.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Constraint.swift; path = Sources/Constraint.swift; sourceTree = "<group>"; };
		979486118B3E90C08386079D57962701 /* SnapKit */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = SnapKit; path = SnapKit.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		98617B298513359CFB98D102BD6C1E1B /* argon2.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = argon2.h; path = Sources/Argon2/include/argon2.h; sourceTree = "<group>"; };
		9A999AD41CC2D8E43A24C54EE3F67190 /* encoding.c */ = {isa = PBXFileReference; includeInIndex = 1; name = encoding.c; path = Sources/Argon2/src/encoding.c; sourceTree = "<group>"; };
		9B5A50F8B7C2B95EC18CEA5D3B17FDE0 /* ConstraintMultiplierTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMultiplierTarget.swift; path = Sources/ConstraintMultiplierTarget.swift; sourceTree = "<group>"; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		A0E4983A10372DA35F8757E613A25F27 /* Pods-CryptaVault-CryptaVaultUITests-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-CryptaVault-CryptaVaultUITests-umbrella.h"; sourceTree = "<group>"; };
		A3A7E1A489CF2809DE0268C444596293 /* Typealiases.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Typealiases.swift; path = Sources/Typealiases.swift; sourceTree = "<group>"; };
		A52FF97F798EF9B4BBCE4E24CAD3F94D /* ConstraintPriority.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintPriority.swift; path = Sources/ConstraintPriority.swift; sourceTree = "<group>"; };
		AAD857F5C0141C816E8FCC4C028D505A /* blake2-impl.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "blake2-impl.h"; path = "Sources/Argon2/src/blake2/blake2-impl.h"; sourceTree = "<group>"; };
		AAFE52443B45C2FAC574F503E334BBE9 /* Argon2Version.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Argon2Version.swift; path = Sources/Swift/Argon2Version.swift; sourceTree = "<group>"; };
		AD4D9FACFCF89069D7F3EB9335BE5237 /* Pods-CryptaVault-CryptaVaultUITests-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-CryptaVault-CryptaVaultUITests-Info.plist"; sourceTree = "<group>"; };
		AE6E681FD8389C11AE14878FA62016FE /* ConstraintRelatableTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintRelatableTarget.swift; path = Sources/ConstraintRelatableTarget.swift; sourceTree = "<group>"; };
		AF99469FA99F5A55CF46B489B39F2B0E /* Argon2SwiftException.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Argon2SwiftException.swift; path = Sources/Swift/Argon2SwiftException.swift; sourceTree = "<group>"; };
		B0138473A3C3BB1B89DC53CCBF893898 /* ConstraintItem.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintItem.swift; path = Sources/ConstraintItem.swift; sourceTree = "<group>"; };
		B0224320DFA022DFA0076D876ED1766D /* ConstraintInsets.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintInsets.swift; path = Sources/ConstraintInsets.swift; sourceTree = "<group>"; };
		B03A9A5ED49568ABCAC04E09A2FAF93E /* Argon2Swift-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Argon2Swift-Info.plist"; sourceTree = "<group>"; };
		B110DBC28F336D822253782B118E223C /* ConstraintMakerRelatable+Extensions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "ConstraintMakerRelatable+Extensions.swift"; path = "Sources/ConstraintMakerRelatable+Extensions.swift"; sourceTree = "<group>"; };
		B49B53A0BC8A4E90F81B4465FE0334AE /* thread.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = thread.h; path = Sources/Argon2/src/thread.h; sourceTree = "<group>"; };
		B4F453180D034592A4879B4A8EFFBBF1 /* ConstraintViewDSL.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintViewDSL.swift; path = Sources/ConstraintViewDSL.swift; sourceTree = "<group>"; };
		B797FA35CA91FC24ED1C161F8B92E257 /* ConstraintConfig.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintConfig.swift; path = Sources/ConstraintConfig.swift; sourceTree = "<group>"; };
		B9DCB5EC0B1CDADD221717CADDF62359 /* SnapKit-SnapKit_Privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "SnapKit-SnapKit_Privacy"; path = SnapKit_Privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		C08E8D2B85539DD83D008BD644B4501A /* Pods-CryptaVault-CryptaVaultUITests */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-CryptaVault-CryptaVaultUITests"; path = Pods_CryptaVault_CryptaVaultUITests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		C0D83686B069E2827F429FA728A012AD /* ConstraintMakerEditable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMakerEditable.swift; path = Sources/ConstraintMakerEditable.swift; sourceTree = "<group>"; };
		C1975ABA651BD4AD225091841EA3E13D /* Pods-CryptaVault-CryptaVaultUITests-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-CryptaVault-CryptaVaultUITests-dummy.m"; sourceTree = "<group>"; };
		C842E2C85183801B11A259FF24FE5D24 /* Argon2Swift-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Argon2Swift-dummy.m"; sourceTree = "<group>"; };
		C8BDCC8D1D8A26E908B67C7E15698C64 /* Pods-CryptaVaultTests-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-CryptaVaultTests-umbrella.h"; sourceTree = "<group>"; };
		C9543D1C7BF8471FE46EE844DFBDA902 /* UILayoutSupport+Extensions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UILayoutSupport+Extensions.swift"; path = "Sources/UILayoutSupport+Extensions.swift"; sourceTree = "<group>"; };
		CAA5D21E2594247E24B96369E5778D68 /* Pods-CryptaVault-CryptaVaultUITests-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-CryptaVault-CryptaVaultUITests-acknowledgements.markdown"; sourceTree = "<group>"; };
		CDEFDC1C28FF21432851B7A17637D7B3 /* Pods-CryptaVault-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-CryptaVault-umbrella.h"; sourceTree = "<group>"; };
		D057D1A95095F346D2D2102E44598CD7 /* Argon2Swift.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Argon2Swift.release.xcconfig; sourceTree = "<group>"; };
		D36DF5E35313F8C69F6E604696C6468A /* ConstraintLayoutSupport.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintLayoutSupport.swift; path = Sources/ConstraintLayoutSupport.swift; sourceTree = "<group>"; };
		D41D49B4DEED0B880AF7ADB3191D9338 /* Pods-CryptaVault-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-CryptaVault-dummy.m"; sourceTree = "<group>"; };
		D51CB4DFCD6566CDCC4E553F92C7431E /* Argon2Swift */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = Argon2Swift; path = Argon2Swift.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DAC9228C0EDAF884E1F2D4C2AF200BF1 /* Pods-CryptaVault-CryptaVaultUITests.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-CryptaVault-CryptaVaultUITests.modulemap"; sourceTree = "<group>"; };
		DE3F559F3049A591BC9A3FE1837F1BAD /* SnapKit-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "SnapKit-umbrella.h"; sourceTree = "<group>"; };
		E552C907BB0F9CBAED8A5BAD540D8A3D /* thread.c */ = {isa = PBXFileReference; includeInIndex = 1; name = thread.c; path = Sources/Argon2/src/thread.c; sourceTree = "<group>"; };
		E669B9FBDDC3A15261E98039724F0907 /* core.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = core.h; path = Sources/Argon2/src/core.h; sourceTree = "<group>"; };
		ECE73ECFD00C29AA25E6805FAA87665F /* Argon2Swift.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Argon2Swift.debug.xcconfig; sourceTree = "<group>"; };
		ED76BBEA13245638D8ED4DAE76E93683 /* Pods-CryptaVaultTests-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-CryptaVaultTests-dummy.m"; sourceTree = "<group>"; };
		F5FC9CF5D16536799DB80F1A32E0B9BC /* ConstraintMakerRelatable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMakerRelatable.swift; path = Sources/ConstraintMakerRelatable.swift; sourceTree = "<group>"; };
		F76C5F03B87F011F19F1FE46DDD6818D /* ConstraintLayoutSupportDSL.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintLayoutSupportDSL.swift; path = Sources/ConstraintLayoutSupportDSL.swift; sourceTree = "<group>"; };
		F7D127F96A6581F289570D7C11168A9F /* ConstraintDescription.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintDescription.swift; path = Sources/ConstraintDescription.swift; sourceTree = "<group>"; };
		FA13DEB23329E77512BC43335CFE51A0 /* Argon2SwiftErrorCode.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Argon2SwiftErrorCode.swift; path = Sources/Swift/Argon2SwiftErrorCode.swift; sourceTree = "<group>"; };
		FB4232671CFEBC3458ED8A5168734BB4 /* Pods-CryptaVaultTests-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-CryptaVaultTests-acknowledgements.markdown"; sourceTree = "<group>"; };
		FC0C8868B24D2573BABB626430B35612 /* Pods-CryptaVault-CryptaVaultUITests-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-CryptaVault-CryptaVaultUITests-acknowledgements.plist"; sourceTree = "<group>"; };
		FC0DEED0E6EA8CB3544D80D6778DF6B4 /* Pods-CryptaVaultTests-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-CryptaVaultTests-acknowledgements.plist"; sourceTree = "<group>"; };
		FE39C839EF05DE33B2B2E8D41430B716 /* encoding.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = encoding.h; path = Sources/Argon2/src/encoding.h; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		33428AC36668E3ED52DB70316F843FB8 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				205EB01AED14BB574DD54EAFE26E4786 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6F955F64B44B6E54406EFF677EBBA644 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A52CB3844E4EF94A6142533B70BE5FF6 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9D5BD5F72AF693698D53FD70ACF8EF56 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A74C779C5D28AE54CAD43F0C13EC0B38 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C83200FE931DC51A7561BF978B9AA82D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				298FC24E88A3A8F83707378E591601DF /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D02633AEA27CF1B6FDC4363D69BC03AF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E8873A1EF31CF10F339AE723CDF081F4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4E8B41AFBEE914ABD496768D44ABA63D /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		077627E0B1CAC4F427487F777AA04AE2 /* Pods-CryptaVault-CryptaVaultUITests */ = {
			isa = PBXGroup;
			children = (
				DAC9228C0EDAF884E1F2D4C2AF200BF1 /* Pods-CryptaVault-CryptaVaultUITests.modulemap */,
				CAA5D21E2594247E24B96369E5778D68 /* Pods-CryptaVault-CryptaVaultUITests-acknowledgements.markdown */,
				FC0C8868B24D2573BABB626430B35612 /* Pods-CryptaVault-CryptaVaultUITests-acknowledgements.plist */,
				C1975ABA651BD4AD225091841EA3E13D /* Pods-CryptaVault-CryptaVaultUITests-dummy.m */,
				57D1B7D5CF49B94E5A372AF2336784DC /* Pods-CryptaVault-CryptaVaultUITests-frameworks.sh */,
				AD4D9FACFCF89069D7F3EB9335BE5237 /* Pods-CryptaVault-CryptaVaultUITests-Info.plist */,
				A0E4983A10372DA35F8757E613A25F27 /* Pods-CryptaVault-CryptaVaultUITests-umbrella.h */,
				2DEFFC159F126EA77BE1BA7860573978 /* Pods-CryptaVault-CryptaVaultUITests.debug.xcconfig */,
				7EBEAE1FC43E382D5A7EB5CEB147A7BC /* Pods-CryptaVault-CryptaVaultUITests.release.xcconfig */,
			);
			name = "Pods-CryptaVault-CryptaVaultUITests";
			path = "Target Support Files/Pods-CryptaVault-CryptaVaultUITests";
			sourceTree = "<group>";
		};
		1831B875D5C96FC5EC8E9FED1A64CCE2 /* Pods-CryptaVault */ = {
			isa = PBXGroup;
			children = (
				607A7153C0032536D3E0B32A292AA948 /* Pods-CryptaVault.modulemap */,
				21D76151EEB30D170EDF38A62E90EC30 /* Pods-CryptaVault-acknowledgements.markdown */,
				167834A86B509659EEEBE846264B475F /* Pods-CryptaVault-acknowledgements.plist */,
				D41D49B4DEED0B880AF7ADB3191D9338 /* Pods-CryptaVault-dummy.m */,
				8312D925DCAFBE97AB3C88D555A6430A /* Pods-CryptaVault-frameworks.sh */,
				8088CFF4BBD27BA472A6B9F8198BF8F1 /* Pods-CryptaVault-Info.plist */,
				CDEFDC1C28FF21432851B7A17637D7B3 /* Pods-CryptaVault-umbrella.h */,
				14DFB0F840BC64B9AE372FAE7FEAF343 /* Pods-CryptaVault.debug.xcconfig */,
				0DA8A12E346CD38D7F746A1C981334A4 /* Pods-CryptaVault.release.xcconfig */,
			);
			name = "Pods-CryptaVault";
			path = "Target Support Files/Pods-CryptaVault";
			sourceTree = "<group>";
		};
		2878FE5FF840C7F7BA4DFC14C41D1C4B /* Argon2Swift */ = {
			isa = PBXGroup;
			children = (
				82737147DDE2C6E0440BA31FF0FF952E /* argon2.c */,
				98617B298513359CFB98D102BD6C1E1B /* argon2.h */,
				0B37B0591B212BA543F594A7664564B2 /* Argon2Swift.h */,
				1E7538A7860BC98782C35FB798CB77B3 /* Argon2Swift.swift */,
				FA13DEB23329E77512BC43335CFE51A0 /* Argon2SwiftErrorCode.swift */,
				AF99469FA99F5A55CF46B489B39F2B0E /* Argon2SwiftException.swift */,
				1C978B2816D33A4D5C19566C0A986067 /* Argon2SwiftResult.swift */,
				8FCC6C2CFE12C0773B43CDF3F2E3CE54 /* Argon2Type.swift */,
				AAFE52443B45C2FAC574F503E334BBE9 /* Argon2Version.swift */,
				8ECB03FB14CB7FE5A624465E7095668D /* blake2.h */,
				AAD857F5C0141C816E8FCC4C028D505A /* blake2-impl.h */,
				452B260A9CD073FF7E21470E9D4B2858 /* blake2b.c */,
				2AAB2A23226B4DA8488E4C90F7FE8EA4 /* blamka-round-ref.h */,
				427A5872C6837F9C7137E70E359C18B7 /* core.c */,
				E669B9FBDDC3A15261E98039724F0907 /* core.h */,
				9A999AD41CC2D8E43A24C54EE3F67190 /* encoding.c */,
				FE39C839EF05DE33B2B2E8D41430B716 /* encoding.h */,
				153DF080F266F1D4DF76D2775C4D723A /* ref.c */,
				61B94E2E0DB8CF61478E5572CB30FD9D /* Salt.swift */,
				E552C907BB0F9CBAED8A5BAD540D8A3D /* thread.c */,
				B49B53A0BC8A4E90F81B4465FE0334AE /* thread.h */,
				B21F9C16AECE1E1EC5AE05D04A2929C3 /* Support Files */,
			);
			name = Argon2Swift;
			path = Argon2Swift;
			sourceTree = "<group>";
		};
		4357F3A0295946F9F4EBD0C21A849814 /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				1831B875D5C96FC5EC8E9FED1A64CCE2 /* Pods-CryptaVault */,
				077627E0B1CAC4F427487F777AA04AE2 /* Pods-CryptaVault-CryptaVaultUITests */,
				6B5D1BDF6F5E82C6B4AC997DE68B9CB8 /* Pods-CryptaVaultTests */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		6B5D1BDF6F5E82C6B4AC997DE68B9CB8 /* Pods-CryptaVaultTests */ = {
			isa = PBXGroup;
			children = (
				67EB7E4A6FD14911128B5CE93D2CE7E8 /* Pods-CryptaVaultTests.modulemap */,
				FB4232671CFEBC3458ED8A5168734BB4 /* Pods-CryptaVaultTests-acknowledgements.markdown */,
				FC0DEED0E6EA8CB3544D80D6778DF6B4 /* Pods-CryptaVaultTests-acknowledgements.plist */,
				ED76BBEA13245638D8ED4DAE76E93683 /* Pods-CryptaVaultTests-dummy.m */,
				19E5B6490CE0B2208824E6C1131D3285 /* Pods-CryptaVaultTests-Info.plist */,
				C8BDCC8D1D8A26E908B67C7E15698C64 /* Pods-CryptaVaultTests-umbrella.h */,
				81D44E04F0CB761E8CDB5AFC164084B7 /* Pods-CryptaVaultTests.debug.xcconfig */,
				7E21F391FD7D0D2F086AACD2A3C4607C /* Pods-CryptaVaultTests.release.xcconfig */,
			);
			name = "Pods-CryptaVaultTests";
			path = "Target Support Files/Pods-CryptaVaultTests";
			sourceTree = "<group>";
		};
		8A4A7FECC40BC45D3B338AB9A3F28F08 /* Products */ = {
			isa = PBXGroup;
			children = (
				D51CB4DFCD6566CDCC4E553F92C7431E /* Argon2Swift */,
				1BB047E6DAA9DF532CABF8A256C33B11 /* Pods-CryptaVault */,
				C08E8D2B85539DD83D008BD644B4501A /* Pods-CryptaVault-CryptaVaultUITests */,
				2A82E00EE9D5683E4D1309E85388B924 /* Pods-CryptaVaultTests */,
				979486118B3E90C08386079D57962701 /* SnapKit */,
				B9DCB5EC0B1CDADD221717CADDF62359 /* SnapKit-SnapKit_Privacy */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		B21F9C16AECE1E1EC5AE05D04A2929C3 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				2059EA97CBEA8FBCDED23B5A48CC0B74 /* Argon2Swift.modulemap */,
				C842E2C85183801B11A259FF24FE5D24 /* Argon2Swift-dummy.m */,
				B03A9A5ED49568ABCAC04E09A2FAF93E /* Argon2Swift-Info.plist */,
				328E0F4C0E80F5645384C3845DE31628 /* Argon2Swift-prefix.pch */,
				8D514D4FD731A4AF585200BD12D959F2 /* Argon2Swift-umbrella.h */,
				ECE73ECFD00C29AA25E6805FAA87665F /* Argon2Swift.debug.xcconfig */,
				D057D1A95095F346D2D2102E44598CD7 /* Argon2Swift.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/Argon2Swift";
			sourceTree = "<group>";
		};
		C7567ABD1591EC46D3C6DBA53998A579 /* Resources */ = {
			isa = PBXGroup;
			children = (
				2C5C54C69A99AA6C35858D267B27DB41 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				D210D550F4EA176C3123ED886F8F87F5 /* Frameworks */,
				D29F22FFFCE7B6DB0E02813C12017DFD /* Pods */,
				8A4A7FECC40BC45D3B338AB9A3F28F08 /* Products */,
				4357F3A0295946F9F4EBD0C21A849814 /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		D210D550F4EA176C3123ED886F8F87F5 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				E4801F62A6B08CD9B5410329F1A18FDE /* iOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		D29F22FFFCE7B6DB0E02813C12017DFD /* Pods */ = {
			isa = PBXGroup;
			children = (
				2878FE5FF840C7F7BA4DFC14C41D1C4B /* Argon2Swift */,
				E736FC9228FE767A08055401702859D3 /* SnapKit */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		E4801F62A6B08CD9B5410329F1A18FDE /* iOS */ = {
			isa = PBXGroup;
			children = (
				384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		E736FC9228FE767A08055401702859D3 /* SnapKit */ = {
			isa = PBXGroup;
			children = (
				90136A13465C8B2C8FD609E0A4C92243 /* Constraint.swift */,
				08BE246A0E2CBCD9BD871C459DAAF292 /* ConstraintAttributes.swift */,
				B797FA35CA91FC24ED1C161F8B92E257 /* ConstraintConfig.swift */,
				0ED8CD3250B09F2EEC3894A576D19C69 /* ConstraintConstantTarget.swift */,
				F7D127F96A6581F289570D7C11168A9F /* ConstraintDescription.swift */,
				2B9ABDB98908EC79254A977815A1F5ED /* ConstraintDirectionalInsets.swift */,
				76257782354EA6B77B19E706E7731AE3 /* ConstraintDirectionalInsetTarget.swift */,
				842C2E7D8592C455CD8D6C78B6BD5838 /* ConstraintDSL.swift */,
				B0224320DFA022DFA0076D876ED1766D /* ConstraintInsets.swift */,
				5AFBC65BA58C17AB0213178CFB5B8A85 /* ConstraintInsetTarget.swift */,
				B0138473A3C3BB1B89DC53CCBF893898 /* ConstraintItem.swift */,
				2B97DCAB42EA7E826A4C5C86BEC41A99 /* ConstraintLayoutGuide.swift */,
				3DD2097F773A5FDBDD2B6401EA5DA3C6 /* ConstraintLayoutGuide+Extensions.swift */,
				55CAA01897701BABF1C9A2EFD3FEC97A /* ConstraintLayoutGuideDSL.swift */,
				D36DF5E35313F8C69F6E604696C6468A /* ConstraintLayoutSupport.swift */,
				F76C5F03B87F011F19F1FE46DDD6818D /* ConstraintLayoutSupportDSL.swift */,
				07FB49216259A1FFDA7DF2CA5A8CF705 /* ConstraintMaker.swift */,
				C0D83686B069E2827F429FA728A012AD /* ConstraintMakerEditable.swift */,
				80FBDCBACD2602EB2621FDE75231F8C4 /* ConstraintMakerExtendable.swift */,
				0E99F3B6A1E1071607EF8FA5EF6B1BB4 /* ConstraintMakerFinalizable.swift */,
				3CE68878159D0F21EF4BEBE070329E84 /* ConstraintMakerPrioritizable.swift */,
				F5FC9CF5D16536799DB80F1A32E0B9BC /* ConstraintMakerRelatable.swift */,
				B110DBC28F336D822253782B118E223C /* ConstraintMakerRelatable+Extensions.swift */,
				9B5A50F8B7C2B95EC18CEA5D3B17FDE0 /* ConstraintMultiplierTarget.swift */,
				36B82DEA0C847797A50F98EA463EECD0 /* ConstraintOffsetTarget.swift */,
				A52FF97F798EF9B4BBCE4E24CAD3F94D /* ConstraintPriority.swift */,
				8AFE4951CDC420D5DBC6B6B8769B6D6F /* ConstraintPriorityTarget.swift */,
				AE6E681FD8389C11AE14878FA62016FE /* ConstraintRelatableTarget.swift */,
				7E55558B1A9F9C6BAA92F2978EDB0BCF /* ConstraintRelation.swift */,
				1514F2874E58B7F7CF7598A4677EE91E /* ConstraintView.swift */,
				520BD0F9D17A462E166B268FB4B63131 /* ConstraintView+Extensions.swift */,
				B4F453180D034592A4879B4A8EFFBBF1 /* ConstraintViewDSL.swift */,
				768706019BAC90E03D429C77573B5D3E /* Debugging.swift */,
				0A63CCBDFA5CC0AC259990C95445C689 /* LayoutConstraint.swift */,
				44DFE8899400050EA6149BD5DBCAF872 /* LayoutConstraintItem.swift */,
				A3A7E1A489CF2809DE0268C444596293 /* Typealiases.swift */,
				C9543D1C7BF8471FE46EE844DFBDA902 /* UILayoutSupport+Extensions.swift */,
				C7567ABD1591EC46D3C6DBA53998A579 /* Resources */,
				E909967187DC426503344BC0E65A8A16 /* Support Files */,
			);
			name = SnapKit;
			path = SnapKit;
			sourceTree = "<group>";
		};
		E909967187DC426503344BC0E65A8A16 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				3FA577F88CFBE03044BBEA2C70488812 /* ResourceBundle-SnapKit_Privacy-SnapKit-Info.plist */,
				6B36F0E756245FC6CF88A30B6D40EE02 /* SnapKit.modulemap */,
				70BB8D3CB593E65CA964AC97601A404C /* SnapKit-dummy.m */,
				8FADF3F4E12F8CC92A603C78F96462D3 /* SnapKit-Info.plist */,
				2A4DE497833EBAE67BFF6233F2148974 /* SnapKit-prefix.pch */,
				DE3F559F3049A591BC9A3FE1837F1BAD /* SnapKit-umbrella.h */,
				4C95FFD2D2B46DF0A2DC18A1702F8559 /* SnapKit.debug.xcconfig */,
				137C9F34CF85DC4B712EFE104B21C1A7 /* SnapKit.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/SnapKit";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		05072726F3012436F145EFFE87E4AA3A /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D0ECCE69F62EEC4D4CCC56BAEDA03C85 /* Pods-CryptaVault-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		15A1E83C04DF10FB17C1305F416AC6F0 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A4B5EA679FE30AD3ADFCA3AC7D40792A /* Pods-CryptaVaultTests-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5B1500FE995B9224E0AF0B42CE93C03B /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				57C4F6EFB30DDD14E960AC2D6B34F904 /* SnapKit-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8640502181C49195D0DB931FC81F3DCD /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				74187130EE6FF609966EEB7D523F43D2 /* argon2.h in Headers */,
				FCAEF5D2CEEA4BA3AA5D479A4AC37F65 /* Argon2Swift.h in Headers */,
				D03404E5BCE900BE79A54461FCAD3DCA /* Argon2Swift-umbrella.h in Headers */,
				90425D2F6C13B6734CEFB86A44FDB8FF /* blake2.h in Headers */,
				9945723039A7F6833A43776DAF377029 /* blake2-impl.h in Headers */,
				207AA154418043D750A07C948AC9002E /* blamka-round-ref.h in Headers */,
				956C47C4AAA836574B5D752DA4C12465 /* core.h in Headers */,
				A5C163CD0CB36508E81C563C9AB00F9B /* encoding.h in Headers */,
				C35CDAD05F62E7033CF41CD268EB6935 /* thread.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E191B16B05487C7173DCEFC05B66BAD2 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7BCA0D2B5DFFAB46ECC4A3C19B23A849 /* Pods-CryptaVault-CryptaVaultUITests-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		19622742EBA51E823D6DAE3F8CDBFAD4 /* SnapKit */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 80CE967CAA1D35721519F892BAF7A19B /* Build configuration list for PBXNativeTarget "SnapKit" */;
			buildPhases = (
				5B1500FE995B9224E0AF0B42CE93C03B /* Headers */,
				F7AC6792C89443C7B212A06E810BAB97 /* Sources */,
				33428AC36668E3ED52DB70316F843FB8 /* Frameworks */,
				1DEDF411E550D85A1218E1655456A9CD /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				BDBCE5FC002765517C0369907EEEFED6 /* PBXTargetDependency */,
			);
			name = SnapKit;
			productName = SnapKit;
			productReference = 979486118B3E90C08386079D57962701 /* SnapKit */;
			productType = "com.apple.product-type.framework";
		};
		2E71E7152F9DFE0FF5D1D4286121A3EB /* Pods-CryptaVaultTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 56CB081E30CA6D04230E0132BA15AD27 /* Build configuration list for PBXNativeTarget "Pods-CryptaVaultTests" */;
			buildPhases = (
				15A1E83C04DF10FB17C1305F416AC6F0 /* Headers */,
				35E90AF35174C7B06C1F1A9E667A95F3 /* Sources */,
				E8873A1EF31CF10F339AE723CDF081F4 /* Frameworks */,
				9441E800D82C8C355BFD0CE72B4A1906 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				4449C2860CEDE63AE2F84FB9654E2828 /* PBXTargetDependency */,
			);
			name = "Pods-CryptaVaultTests";
			productName = Pods_CryptaVaultTests;
			productReference = 2A82E00EE9D5683E4D1309E85388B924 /* Pods-CryptaVaultTests */;
			productType = "com.apple.product-type.framework";
		};
		724A2E2E8B6E46C2B2ECD85777BEA9C7 /* Pods-CryptaVault-CryptaVaultUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D92E85533C7DD0A7409CFDE056FE0F7F /* Build configuration list for PBXNativeTarget "Pods-CryptaVault-CryptaVaultUITests" */;
			buildPhases = (
				E191B16B05487C7173DCEFC05B66BAD2 /* Headers */,
				5DAFE2D8D5A4D256E90316101B42A8EF /* Sources */,
				C83200FE931DC51A7561BF978B9AA82D /* Frameworks */,
				45DFADAF9A4BA44FB7E2AE562C2525BB /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				3ADA9E9328384DB5A454FD29C0D1408F /* PBXTargetDependency */,
				FEC8A3C0CD41ED33DC79BB871708C562 /* PBXTargetDependency */,
			);
			name = "Pods-CryptaVault-CryptaVaultUITests";
			productName = Pods_CryptaVault_CryptaVaultUITests;
			productReference = C08E8D2B85539DD83D008BD644B4501A /* Pods-CryptaVault-CryptaVaultUITests */;
			productType = "com.apple.product-type.framework";
		};
		774F69F884C9EC391E56B0EF4A748233 /* Argon2Swift */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2F8E1158F085714DFB5D44CCB76C48B6 /* Build configuration list for PBXNativeTarget "Argon2Swift" */;
			buildPhases = (
				8640502181C49195D0DB931FC81F3DCD /* Headers */,
				C6668A092E57EB2A829505D413378875 /* Sources */,
				6F955F64B44B6E54406EFF677EBBA644 /* Frameworks */,
				517A586BB5B1F2AE4A04BF58DE25753F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Argon2Swift;
			productName = Argon2Swift;
			productReference = D51CB4DFCD6566CDCC4E553F92C7431E /* Argon2Swift */;
			productType = "com.apple.product-type.framework";
		};
		8A8DB685241263AFDF5E6B20FE67B93A /* SnapKit-SnapKit_Privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 710DBBA432DA99FA9D512632048D8B58 /* Build configuration list for PBXNativeTarget "SnapKit-SnapKit_Privacy" */;
			buildPhases = (
				12D4B569402E6F9C41778FC9BCC3A67B /* Sources */,
				D02633AEA27CF1B6FDC4363D69BC03AF /* Frameworks */,
				0039ADAA309DE4B0D095E4F0722246A4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "SnapKit-SnapKit_Privacy";
			productName = SnapKit_Privacy;
			productReference = B9DCB5EC0B1CDADD221717CADDF62359 /* SnapKit-SnapKit_Privacy */;
			productType = "com.apple.product-type.bundle";
		};
		F6790A9A99E70846CC3D02929637929E /* Pods-CryptaVault */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0326B7799165B08DF5851BBFB1FEEB2A /* Build configuration list for PBXNativeTarget "Pods-CryptaVault" */;
			buildPhases = (
				05072726F3012436F145EFFE87E4AA3A /* Headers */,
				416FC6CBCF41C1D8145C36048D3C8EB8 /* Sources */,
				9D5BD5F72AF693698D53FD70ACF8EF56 /* Frameworks */,
				BB1649BB83A8BD28016A242F5263E4C7 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				E1725444BE09A209A4152449A6620A43 /* PBXTargetDependency */,
				20E1DFC553598822168EB71C494815E1 /* PBXTargetDependency */,
			);
			name = "Pods-CryptaVault";
			productName = Pods_CryptaVault;
			productReference = 1BB047E6DAA9DF532CABF8A256C33B11 /* Pods-CryptaVault */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			minimizedProjectReferenceProxies = 0;
			preferredProjectObjectVersion = 77;
			productRefGroup = 8A4A7FECC40BC45D3B338AB9A3F28F08 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				774F69F884C9EC391E56B0EF4A748233 /* Argon2Swift */,
				F6790A9A99E70846CC3D02929637929E /* Pods-CryptaVault */,
				724A2E2E8B6E46C2B2ECD85777BEA9C7 /* Pods-CryptaVault-CryptaVaultUITests */,
				2E71E7152F9DFE0FF5D1D4286121A3EB /* Pods-CryptaVaultTests */,
				19622742EBA51E823D6DAE3F8CDBFAD4 /* SnapKit */,
				8A8DB685241263AFDF5E6B20FE67B93A /* SnapKit-SnapKit_Privacy */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		0039ADAA309DE4B0D095E4F0722246A4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C2B0677591B11454F8A74199F835954D /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1DEDF411E550D85A1218E1655456A9CD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9E0045B41BFE697DB4ADE151228024D2 /* SnapKit-SnapKit_Privacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		45DFADAF9A4BA44FB7E2AE562C2525BB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		517A586BB5B1F2AE4A04BF58DE25753F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9441E800D82C8C355BFD0CE72B4A1906 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BB1649BB83A8BD28016A242F5263E4C7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		12D4B569402E6F9C41778FC9BCC3A67B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		35E90AF35174C7B06C1F1A9E667A95F3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B4BB4F9DDDBD927B7B0F54F903FE3447 /* Pods-CryptaVaultTests-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		416FC6CBCF41C1D8145C36048D3C8EB8 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E024C786320E92EF0802680E5C69E60F /* Pods-CryptaVault-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5DAFE2D8D5A4D256E90316101B42A8EF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				86F500C9ED0D1869F16AE06A029D4503 /* Pods-CryptaVault-CryptaVaultUITests-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C6668A092E57EB2A829505D413378875 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9A587F4A4C9A23A694A617BE1466296F /* argon2.c in Sources */,
				33474669ECB03956E9E4C8EAA979BD02 /* Argon2Swift.swift in Sources */,
				A26010B4AC30AB908102417666F48F06 /* Argon2Swift-dummy.m in Sources */,
				D7CC4F81116718D47E0F1B95CD5D188A /* Argon2SwiftErrorCode.swift in Sources */,
				608016C976D0B713FADDFA2E690B0E91 /* Argon2SwiftException.swift in Sources */,
				A7BE54E5F8A96F3D0E21B79F7E0DB365 /* Argon2SwiftResult.swift in Sources */,
				FD3EE9637D0DEA764FAFD0DF4A91BF5D /* Argon2Type.swift in Sources */,
				DD4CF0EC69CDA3BC37D2625E406B0B07 /* Argon2Version.swift in Sources */,
				88A81F31EE132CEEC2BF09D8822DB822 /* blake2b.c in Sources */,
				7521665F6055A7A0D4346F82AC4F0AA6 /* core.c in Sources */,
				A7B6F9C2AC29BCCA0FE9FAE4808BB84D /* encoding.c in Sources */,
				2F6FAD6E0909054DC5509040333F2B54 /* ref.c in Sources */,
				70E64058741152093089E1B84042415C /* Salt.swift in Sources */,
				45A7A7EA8B7E44BACC20772EDAED35BE /* thread.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F7AC6792C89443C7B212A06E810BAB97 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6E39129FC8643A70C276801FEF4C280D /* Constraint.swift in Sources */,
				0CA7A132ABE7018DE9295456732F38BB /* ConstraintAttributes.swift in Sources */,
				F9EBA65892D78A31C068D727D84BCB88 /* ConstraintConfig.swift in Sources */,
				CE593943A9E7CF83822CF60304BCAD43 /* ConstraintConstantTarget.swift in Sources */,
				7AF516B98D45391B909D507D0244104C /* ConstraintDescription.swift in Sources */,
				1194E62AA3F6F506799B1A43B16942B5 /* ConstraintDirectionalInsets.swift in Sources */,
				E37671A03B4C17A1CF3766A6125833BB /* ConstraintDirectionalInsetTarget.swift in Sources */,
				868A9F524A7985BDA1EA124D9BF4CA63 /* ConstraintDSL.swift in Sources */,
				AF760C78F1C7E11BF7CB9E9B29903530 /* ConstraintInsets.swift in Sources */,
				2B2EB369550CE92CEEFCBFD3D32B8A3F /* ConstraintInsetTarget.swift in Sources */,
				5922A6A0AE7152CF436356B3556F1835 /* ConstraintItem.swift in Sources */,
				59F34874DA4ABB2F5C4E09EA6865936B /* ConstraintLayoutGuide.swift in Sources */,
				E3D779DEE753C0B0D33BA8E73A980265 /* ConstraintLayoutGuide+Extensions.swift in Sources */,
				3D3B646B4988314275B40E97BEB16C7F /* ConstraintLayoutGuideDSL.swift in Sources */,
				B0875E3AB8718E7DFE5C53497C02A15E /* ConstraintLayoutSupport.swift in Sources */,
				064D909CD827405E8DCC309DB1B7775A /* ConstraintLayoutSupportDSL.swift in Sources */,
				4F4DEB687C0E4834A5B291DEE0651D6A /* ConstraintMaker.swift in Sources */,
				C14F10B663FE2898EACAB90C202B3F50 /* ConstraintMakerEditable.swift in Sources */,
				D4218DA55B2BA45937589200CC0DF1FB /* ConstraintMakerExtendable.swift in Sources */,
				B903049E7C1BED7918DAB208754107C7 /* ConstraintMakerFinalizable.swift in Sources */,
				AABEF13464BA7F4621BD94736C1D057C /* ConstraintMakerPrioritizable.swift in Sources */,
				BDA5C7CC91E86448237CF40954FAC5AF /* ConstraintMakerRelatable.swift in Sources */,
				8BABA32F7B94A25D8E9208C0A8D90B2E /* ConstraintMakerRelatable+Extensions.swift in Sources */,
				883EDEE1C699497CF2A77C3B8A32A790 /* ConstraintMultiplierTarget.swift in Sources */,
				3577F172FA68CBAE47CFEE6FE25C5404 /* ConstraintOffsetTarget.swift in Sources */,
				09E1F569A93FAD4B9149E30B9301F44A /* ConstraintPriority.swift in Sources */,
				DBA4803F4765E1650B8C6841157F5D73 /* ConstraintPriorityTarget.swift in Sources */,
				C07CB3E9A4D1BF00F841E4285629A2B2 /* ConstraintRelatableTarget.swift in Sources */,
				ECC5C2ADC2682F9171FEA22AF10DCE53 /* ConstraintRelation.swift in Sources */,
				86CAB01D950C8BC35EDE0BDC01A2500B /* ConstraintView.swift in Sources */,
				0DE5DB9C6227B3416778D8417DD95EA9 /* ConstraintView+Extensions.swift in Sources */,
				7D42390CDB4FA147504B03DA2A174A0C /* ConstraintViewDSL.swift in Sources */,
				AE224EDB6D044C0FE86B086E950FC2F9 /* Debugging.swift in Sources */,
				BF1AE4D97E813B95C43EA4A298B973D1 /* LayoutConstraint.swift in Sources */,
				C6F45595676957ADBEC18EB3F23EAEC4 /* LayoutConstraintItem.swift in Sources */,
				BA2FB695DEB0D179253EEB8DFCE3578B /* SnapKit-dummy.m in Sources */,
				C6A4302ACE006C4E2CDD481287E2916B /* Typealiases.swift in Sources */,
				F6F33E8B268F3D41075374D95B8088DC /* UILayoutSupport+Extensions.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		20E1DFC553598822168EB71C494815E1 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SnapKit;
			target = 19622742EBA51E823D6DAE3F8CDBFAD4 /* SnapKit */;
			targetProxy = 499E2B7A89C793EC8522F937D5FCF2E3 /* PBXContainerItemProxy */;
		};
		3ADA9E9328384DB5A454FD29C0D1408F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Argon2Swift;
			target = 774F69F884C9EC391E56B0EF4A748233 /* Argon2Swift */;
			targetProxy = 438C379A69C6D0D46118E90DD9783E49 /* PBXContainerItemProxy */;
		};
		4449C2860CEDE63AE2F84FB9654E2828 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "Pods-CryptaVault";
			target = F6790A9A99E70846CC3D02929637929E /* Pods-CryptaVault */;
			targetProxy = 174CAF5500A1DCCCA4BFEC05B1B72476 /* PBXContainerItemProxy */;
		};
		BDBCE5FC002765517C0369907EEEFED6 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "SnapKit-SnapKit_Privacy";
			target = 8A8DB685241263AFDF5E6B20FE67B93A /* SnapKit-SnapKit_Privacy */;
			targetProxy = E08D009B4980AAF9419F2D4DB1F177F2 /* PBXContainerItemProxy */;
		};
		E1725444BE09A209A4152449A6620A43 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Argon2Swift;
			target = 774F69F884C9EC391E56B0EF4A748233 /* Argon2Swift */;
			targetProxy = 58481F273575D29189E12ADA6E2FFEF3 /* PBXContainerItemProxy */;
		};
		FEC8A3C0CD41ED33DC79BB871708C562 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SnapKit;
			target = 19622742EBA51E823D6DAE3F8CDBFAD4 /* SnapKit */;
			targetProxy = 87B0354C3433F475ABCD7065C356B2A3 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		05AA7DA77FAB1516AADBE25C56DEB5C2 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0DA8A12E346CD38D7F746A1C981334A4 /* Pods-CryptaVault.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-CryptaVault/Pods-CryptaVault-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 16.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-CryptaVault/Pods-CryptaVault.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		280B99BE711A4B651FD393CFBB6F0763 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2DEFFC159F126EA77BE1BA7860573978 /* Pods-CryptaVault-CryptaVaultUITests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-CryptaVault-CryptaVaultUITests/Pods-CryptaVault-CryptaVaultUITests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 16.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-CryptaVault-CryptaVaultUITests/Pods-CryptaVault-CryptaVaultUITests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		2C3B14663810A1ED72961FA9FA807251 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 137C9F34CF85DC4B712EFE104B21C1A7 /* SnapKit.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SnapKit";
				IBSC_MODULE = SnapKit;
				INFOPLIST_FILE = "Target Support Files/SnapKit/ResourceBundle-SnapKit_Privacy-SnapKit-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = SnapKit_Privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		2C995BB77188FDA344E1BD3552A69ADE /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D057D1A95095F346D2D2102E44598CD7 /* Argon2Swift.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/Argon2Swift/Argon2Swift-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/Argon2Swift/Argon2Swift-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/Argon2Swift/Argon2Swift.modulemap";
				PRODUCT_MODULE_NAME = Argon2Swift;
				PRODUCT_NAME = Argon2Swift;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		446BA1B9A1F1F2F95D04E5F82EB1B0F4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.2;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		5D47E2EFAC6FAC81A782D9BFD8736F53 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.2;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		729A7600D80538D015661371ACC3534F /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = ECE73ECFD00C29AA25E6805FAA87665F /* Argon2Swift.debug.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/Argon2Swift/Argon2Swift-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/Argon2Swift/Argon2Swift-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/Argon2Swift/Argon2Swift.modulemap";
				PRODUCT_MODULE_NAME = Argon2Swift;
				PRODUCT_NAME = Argon2Swift;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		7D44D28D1B23FB461B29C7ADB90AF87F /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7E21F391FD7D0D2F086AACD2A3C4607C /* Pods-CryptaVaultTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-CryptaVaultTests/Pods-CryptaVaultTests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 16.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-CryptaVaultTests/Pods-CryptaVaultTests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		9D0D7572FEDA4CC9E75E7387846AFE57 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4C95FFD2D2B46DF0A2DC18A1702F8559 /* SnapKit.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/SnapKit/SnapKit-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/SnapKit/SnapKit-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/SnapKit/SnapKit.modulemap";
				PRODUCT_MODULE_NAME = SnapKit;
				PRODUCT_NAME = SnapKit;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		A043C85C86DB924FFE3A3A0D6BA410D7 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 81D44E04F0CB761E8CDB5AFC164084B7 /* Pods-CryptaVaultTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-CryptaVaultTests/Pods-CryptaVaultTests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 16.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-CryptaVaultTests/Pods-CryptaVaultTests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		B8BE4BA96DDD4632E19C6B04BB291461 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4C95FFD2D2B46DF0A2DC18A1702F8559 /* SnapKit.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SnapKit";
				IBSC_MODULE = SnapKit;
				INFOPLIST_FILE = "Target Support Files/SnapKit/ResourceBundle-SnapKit_Privacy-SnapKit-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = SnapKit_Privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		BAD20131EC29650C6737E66854A3A9FD /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 137C9F34CF85DC4B712EFE104B21C1A7 /* SnapKit.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/SnapKit/SnapKit-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/SnapKit/SnapKit-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/SnapKit/SnapKit.modulemap";
				PRODUCT_MODULE_NAME = SnapKit;
				PRODUCT_NAME = SnapKit;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		C0DF6C09D68D4EABA7A6223D25C7E6D8 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 14DFB0F840BC64B9AE372FAE7FEAF343 /* Pods-CryptaVault.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-CryptaVault/Pods-CryptaVault-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 16.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-CryptaVault/Pods-CryptaVault.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		C4E7D3125854EB1B92814373A9FFA508 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7EBEAE1FC43E382D5A7EB5CEB147A7BC /* Pods-CryptaVault-CryptaVaultUITests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-CryptaVault-CryptaVaultUITests/Pods-CryptaVault-CryptaVaultUITests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 16.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-CryptaVault-CryptaVaultUITests/Pods-CryptaVault-CryptaVaultUITests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		0326B7799165B08DF5851BBFB1FEEB2A /* Build configuration list for PBXNativeTarget "Pods-CryptaVault" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C0DF6C09D68D4EABA7A6223D25C7E6D8 /* Debug */,
				05AA7DA77FAB1516AADBE25C56DEB5C2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2F8E1158F085714DFB5D44CCB76C48B6 /* Build configuration list for PBXNativeTarget "Argon2Swift" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				729A7600D80538D015661371ACC3534F /* Debug */,
				2C995BB77188FDA344E1BD3552A69ADE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				446BA1B9A1F1F2F95D04E5F82EB1B0F4 /* Debug */,
				5D47E2EFAC6FAC81A782D9BFD8736F53 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		56CB081E30CA6D04230E0132BA15AD27 /* Build configuration list for PBXNativeTarget "Pods-CryptaVaultTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A043C85C86DB924FFE3A3A0D6BA410D7 /* Debug */,
				7D44D28D1B23FB461B29C7ADB90AF87F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		710DBBA432DA99FA9D512632048D8B58 /* Build configuration list for PBXNativeTarget "SnapKit-SnapKit_Privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B8BE4BA96DDD4632E19C6B04BB291461 /* Debug */,
				2C3B14663810A1ED72961FA9FA807251 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		80CE967CAA1D35721519F892BAF7A19B /* Build configuration list for PBXNativeTarget "SnapKit" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9D0D7572FEDA4CC9E75E7387846AFE57 /* Debug */,
				BAD20131EC29650C6737E66854A3A9FD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		D92E85533C7DD0A7409CFDE056FE0F7F /* Build configuration list for PBXNativeTarget "Pods-CryptaVault-CryptaVaultUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				280B99BE711A4B651FD393CFBB6F0763 /* Debug */,
				C4E7D3125854EB1B92814373A9FFA508 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
