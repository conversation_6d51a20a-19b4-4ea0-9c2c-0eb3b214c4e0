//
//  MainTabBarController.swift
//  CryptaVault
//
//  Created by <PERSON><PERSON><PERSON> on 2025/8/4.
//

import UIKit

class MainTabBarController: UITabBarController {
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupViewControllers()
        setupTabBarAppearance()
    }
    
    private func setupViewControllers() {
        let passwordListVC = ViewController()
        passwordListVC.tabBarItem = UITabBarItem(title: "机密文档", image: UIImage(systemName: "doc.text"), tag: 0)
        
        let passwordGeneratorVC = PasswordGeneratorViewController()
        passwordGeneratorVC.tabBarItem = UITabBarItem(title: "密码机", image: UIImage(systemName: "number"), tag: 1)
        
        let accountVC = AccountViewController()
        accountVC.tabBarItem = UITabBarItem(title: "账户", image: UIImage(systemName: "person"), tag: 2)
        
        viewControllers = [passwordListVC, passwordGeneratorVC, accountVC]
        selectedIndex = 0
    }
    
    private func setupTabBarAppearance() {
        tabBar.backgroundColor = .systemBackground
        tabBar.layer.borderWidth = 0.5
        tabBar.layer.borderColor = UIColor.systemGray4.cgColor
        
        tabBar.tintColor = .systemBlue
        tabBar.unselectedItemTintColor = .systemGray
    }
}