//
//  LoginViewController.swift
//  CryptaVault
//
//  Created by <PERSON><PERSON><PERSON> on 2025/8/4.
//

import UIKit
import SnapKit

class LoginViewController: UIViewController {
    private let titleLabel = UILabel()
    private let emailLabel = UILabel()
    private let emailTextField = UITextField()
    private let passwordLabel = UILabel()
    private let passwordTextField = UITextField()
    private let googleLoginButton = UIButton(type: .system)
    private let facebookLoginButton = UIButton(type: .system)
    private let twitterLoginButton = UIButton(type: .system)
    private let exitLoginButton = UIButton(type: .system)
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    private func setupUI() {
        view.backgroundColor = .systemBackground
        
        titleLabel.text = "登录"
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        titleLabel.textAlignment = .center
        
        setupLabels()
        setupTextField(emailTextField, placeholder: "邮箱", text: "<EMAIL>")
        setupTextField(passwordTextField, placeholder: "密码", text: "••••••••", isSecure: true)
        
        setupOAuthButton(googleLoginButton, title: "Google 登录", backgroundColor: .systemBackground)
        setupOAuthButton(facebookLoginButton, title: "Facebook 登录", backgroundColor: .systemBackground)
        setupOAuthButton(twitterLoginButton, title: "X 登录", backgroundColor: .systemBackground)
        
        exitLoginButton.setTitle("退出登录", for: .normal)
        exitLoginButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        exitLoginButton.backgroundColor = UIColor.systemRed
        exitLoginButton.setTitleColor(.white, for: .normal)
        exitLoginButton.layer.cornerRadius = 8
        exitLoginButton.addTarget(self, action: #selector(exitLoginTapped), for: .touchUpInside)
        
        addSubviews()
        setupConstraints()
    }
    
    private func setupLabels() {
        emailLabel.text = "邮箱:"
        emailLabel.font = UIFont.systemFont(ofSize: 16)
        emailLabel.textColor = .label
        
        passwordLabel.text = "密码:"
        passwordLabel.font = UIFont.systemFont(ofSize: 16)
        passwordLabel.textColor = .label
    }
    
    private func setupTextField(_ textField: UITextField, placeholder: String, text: String, isSecure: Bool = false) {
        textField.placeholder = placeholder
        textField.text = text
        textField.font = UIFont.systemFont(ofSize: 16)
        textField.backgroundColor = .systemBackground
        textField.layer.cornerRadius = 8
        textField.layer.borderWidth = 1
        textField.layer.borderColor = UIColor.systemGray4.cgColor
        textField.isSecureTextEntry = isSecure
        
        let paddingView = UIView(frame: CGRect(x: 0, y: 0, width: 12, height: textField.frame.height))
        textField.leftView = paddingView
        textField.leftViewMode = .always
        textField.rightView = paddingView
        textField.rightViewMode = .always
    }
    
    private func setupOAuthButton(_ button: UIButton, title: String, backgroundColor: UIColor) {
        button.setTitle(title, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        button.backgroundColor = backgroundColor
        button.setTitleColor(.label, for: .normal)
        button.layer.cornerRadius = 8
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor.systemGray4.cgColor
        
        switch title {
        case "Google 登录":
            button.addTarget(self, action: #selector(googleLoginTapped), for: .touchUpInside)
        case "Facebook 登录":
            button.addTarget(self, action: #selector(facebookLoginTapped), for: .touchUpInside)
        case "X 登录":
            button.addTarget(self, action: #selector(twitterLoginTapped), for: .touchUpInside)
        default:
            break
        }
    }
    
    private func addSubviews() {
        view.addSubview(titleLabel)
        view.addSubview(emailLabel)
        view.addSubview(emailTextField)
        view.addSubview(passwordLabel)
        view.addSubview(passwordTextField)
        view.addSubview(googleLoginButton)
        view.addSubview(facebookLoginButton)
        view.addSubview(twitterLoginButton)
        view.addSubview(exitLoginButton)
    }
    
    private func setupConstraints() {
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide).offset(20)
            make.centerX.equalToSuperview()
        }
        
        emailLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(40)
            make.leading.equalToSuperview().offset(20)
        }
        
        emailTextField.snp.makeConstraints { make in
            make.top.equalTo(emailLabel.snp.bottom).offset(8)
            make.leading.equalToSuperview().offset(20)
            make.trailing.equalToSuperview().offset(-20)
            make.height.equalTo(44)
        }
        
        passwordLabel.snp.makeConstraints { make in
            make.top.equalTo(emailTextField.snp.bottom).offset(20)
            make.leading.equalToSuperview().offset(20)
        }
        
        passwordTextField.snp.makeConstraints { make in
            make.top.equalTo(passwordLabel.snp.bottom).offset(8)
            make.leading.equalToSuperview().offset(20)
            make.trailing.equalToSuperview().offset(-20)
            make.height.equalTo(44)
        }
        
        googleLoginButton.snp.makeConstraints { make in
            make.top.equalTo(passwordTextField.snp.bottom).offset(40)
            make.leading.equalToSuperview().offset(20)
            make.trailing.equalToSuperview().offset(-20)
            make.height.equalTo(44)
        }
        
        facebookLoginButton.snp.makeConstraints { make in
            make.top.equalTo(googleLoginButton.snp.bottom).offset(16)
            make.leading.equalToSuperview().offset(20)
            make.trailing.equalToSuperview().offset(-20)
            make.height.equalTo(44)
        }
        
        twitterLoginButton.snp.makeConstraints { make in
            make.top.equalTo(facebookLoginButton.snp.bottom).offset(16)
            make.leading.equalToSuperview().offset(20)
            make.trailing.equalToSuperview().offset(-20)
            make.height.equalTo(44)
        }
        
        exitLoginButton.snp.makeConstraints { make in
            make.top.equalTo(twitterLoginButton.snp.bottom).offset(20)
            make.leading.equalToSuperview().offset(20)
            make.trailing.equalToSuperview().offset(-20)
            make.height.equalTo(44)
        }
    }
    
    @objc private func googleLoginTapped() {
        OAuthManager.shared.loginWithGoogle { [weak self] result in
            DispatchQueue.main.async {
                self?.handleOAuthResult(result, provider: "Google")
            }
        }
    }
    
    @objc private func facebookLoginTapped() {
        OAuthManager.shared.loginWithFacebook { [weak self] result in
            DispatchQueue.main.async {
                self?.handleOAuthResult(result, provider: "Facebook")
            }
        }
    }
    
    @objc private func twitterLoginTapped() {
        OAuthManager.shared.loginWithTwitter { [weak self] result in
            DispatchQueue.main.async {
                self?.handleOAuthResult(result, provider: "Twitter")
            }
        }
    }
    
    @objc private func exitLoginTapped() {
        OAuthManager.shared.logout { [weak self] success in
            DispatchQueue.main.async {
                if success {
                    let alert = UIAlertController(title: "已退出", message: "已成功退出登录", preferredStyle: .alert)
                    alert.addAction(UIAlertAction(title: "确定", style: .default))
                    self?.present(alert, animated: true)
                }
            }
        }
    }
    
    private func handleOAuthResult(_ result: Result<OAuthToken, Error>, provider: String) {
        switch result {
        case .success(let token):
            let alert = UIAlertController(title: "登录成功", message: "\(provider) 登录成功", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
                self.navigationToMainApp()
            })
            present(alert, animated: true)
            
        case .failure(let error):
            let alert = UIAlertController(title: "登录失败", message: error.localizedDescription, preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
        }
    }
    
    private func navigationToMainApp() {
        if let sceneDelegate = view.window?.windowScene?.delegate as? SceneDelegate {
            sceneDelegate.window?.rootViewController = MainTabBarController()
        }
    }
}