//
//  APIExamples.swift
//  CryptaVault
//
//  Created by <PERSON><PERSON><PERSON> on 2025/8/4.
//

import Foundation

// MARK: - Example API Request Models

// User Profile Request
struct UserProfileRequest: APIRequest {
    typealias Response = UserProfile
    
    let path = "/user/profile"
    let method: HTTPMethod = .GET
    let parameters: [String: Any]? = nil
    let headers: [String: String]?
    
    init(accessToken: String) {
        self.headers = ["Authorization": "Bearer \(accessToken)"]
    }
}

struct UserProfile: Codable {
    let id: String
    let email: String
    let name: String
    let avatarURL: String?
    
    enum CodingKeys: String, CodingKey {
        case id, email, name
        case avatarURL = "avatar_url"
    }
}

// Password Vault Request
struct PasswordVaultRequest: APIRequest {
    typealias Response = PasswordVaultResponse
    
    let path = "/vault/passwords"
    let method: HTTPMethod = .GET
    let parameters: [String: Any]?
    let headers: [String: String]?
    
    init(accessToken: String, limit: Int = 20, offset: Int = 0) {
        self.parameters = ["limit": limit, "offset": offset]
        self.headers = ["Authorization": "Bearer \(accessToken)"]
    }
}

struct PasswordVaultResponse: Codable {
    let passwords: [VaultPasswordEntry]
    let totalCount: Int
    let hasMore: Bool
    
    enum CodingKeys: String, CodingKey {
        case passwords
        case totalCount = "total_count"
        case hasMore = "has_more"
    }
}

struct VaultPasswordEntry: Codable {
    let id: String
    let title: String
    let username: String
    let website: String
    let createdAt: Date
    let updatedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case id, title, username, website
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// Add Password Request
struct AddPasswordRequest: APIRequest {
    typealias Response = AddPasswordResponse
    
    let path = "/vault/passwords"
    let method: HTTPMethod = .POST
    let parameters: [String: Any]?
    let headers: [String: String]?
    
    init(accessToken: String, title: String, username: String, website: String, password: String) {
        self.parameters = [
            "title": title,
            "username": username,
            "website": website,
            "password": password
        ]
        self.headers = [
            "Authorization": "Bearer \(accessToken)",
            "Content-Type": "application/json"
        ]
    }
}

struct AddPasswordResponse: Codable {
    let id: String
    let success: Bool
    let message: String
}

// MARK: - Example Usage

class VaultAPIService {
    private let apiManager = APIManager.shared
    
    func fetchUserProfile(completion: @escaping (Result<UserProfile, APIError>) -> Void) {
        guard let token = OAuthManager.shared.getStoredToken() else {
            completion(.failure(.networkError(NSError(domain: "No access token", code: 401))))
            return
        }
        
        let request = UserProfileRequest(accessToken: token.accessToken)
        
        apiManager.request(
            request,
            onError: { error in
                print("API Error: \(error.localizedDescription)")
                completion(.failure(error))
            }
        ) { profile in
            completion(.success(profile))
        }
    }
    
    func fetchPasswordVault(completion: @escaping (Result<PasswordVaultResponse, APIError>) -> Void) {
        guard let token = OAuthManager.shared.getStoredToken() else {
            completion(.failure(.networkError(NSError(domain: "No access token", code: 401))))
            return
        }
        
        let request = PasswordVaultRequest(accessToken: token.accessToken)
        apiManager.request(request, completion: completion)
    }
    
    func addPassword(title: String, username: String, website: String, password: String, completion: @escaping (Result<AddPasswordResponse, APIError>) -> Void) {
        guard let token = OAuthManager.shared.getStoredToken() else {
            completion(.failure(.networkError(NSError(domain: "No access token", code: 401))))
            return
        }
        
        let request = AddPasswordRequest(
            accessToken: token.accessToken,
            title: title,
            username: username,
            website: website,
            password: password
        )
        
        apiManager.request(request, completion: completion)
    }
    
    // Example with async/await (iOS 13+)
    @available(iOS 13.0, *)
    func fetchUserProfileAsync() async throws -> UserProfile {
        guard let token = OAuthManager.shared.getStoredToken() else {
            throw APIError.networkError(NSError(domain: "No access token", code: 401))
        }
        
        let request = UserProfileRequest(accessToken: token.accessToken)
        return try await apiManager.request(request)
    }
}

// MARK: - Configuration Examples

extension APIManager {
    static func configureForProduction() -> APIManager {
        return shared.updateConfiguration(
            baseURL: "https://api.cryptavault.com/v1",
            timeout: 30.0,
            retryCount: 3
        )
    }
    
    static func configureForDevelopment() -> APIManager {
        return shared.updateConfiguration(
            baseURL: "https://dev-api.cryptavault.com/v1",
            timeout: 60.0,
            retryCount: 1
        )
    }
}