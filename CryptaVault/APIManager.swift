//
//  APIManager.swift
//  CryptaVault
//
//  Created by <PERSON><PERSON><PERSON> on 2025/8/4.
//

import Foundation

// MARK: - API Configuration
struct APIConfiguration {
    let baseURL: String
    let timeout: TimeInterval
    let retryCount: Int
    
    init(baseURL: String, timeout: TimeInterval = 30.0, retryCount: Int = 3) {
        self.baseURL = baseURL
        self.timeout = timeout
        self.retryCount = retryCount
    }
}

// MARK: - API Request Protocol
protocol APIRequest {
    associatedtype Response: Codable
    
    var path: String { get }
    var method: HTTPMethod { get }
    var parameters: [String: Any]? { get }
    var headers: [String: String]? { get }
}

// MARK: - HTTP Method
enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case PUT = "PUT"
    case DELETE = "DELETE"
    case PATCH = "PATCH"
}

// MARK: - API Error
enum APIError: Error, LocalizedError {
    case invalidURL
    case noData
    case decodingE<PERSON><PERSON>(Error)
    case encodingError(Error)
    case networkError(Error)
    case httpError(Int, String?)
    case retryLimitExceeded
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid URL"
        case .noData:
            return "No data received"
        case .decodingError(let error):
            return "Decoding error: \(error.localizedDescription)"
        case .encodingError(let error):
            return "Encoding error: \(error.localizedDescription)"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        case .httpError(let statusCode, let message):
            return "HTTP error \(statusCode): \(message ?? "Unknown error")"
        case .retryLimitExceeded:
            return "Retry limit exceeded"
        }
    }
}

// MARK: - API Manager
class APIManager {
    static let shared = APIManager()
    
    private let session: URLSession
    private let configuration: APIConfiguration
    private let encoder: JSONEncoder
    private let decoder: JSONDecoder
    
    private init(configuration: APIConfiguration = APIConfiguration(baseURL: "https://api.cryptavault.com")) {
        self.configuration = configuration
        
        let sessionConfig = URLSessionConfiguration.default
        sessionConfig.timeoutIntervalForRequest = configuration.timeout
        sessionConfig.timeoutIntervalForResource = configuration.timeout * 2
        self.session = URLSession(configuration: sessionConfig)
        
        self.encoder = JSONEncoder()
        self.decoder = JSONDecoder()
        
        // Configure date formatting if needed
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        encoder.dateEncodingStrategy = .formatted(dateFormatter)
        decoder.dateDecodingStrategy = .formatted(dateFormatter)
    }
    
    // MARK: - Generic Request Method
    func request<T: APIRequest>(
        _ request: T,
        completion: @escaping (Result<T.Response, APIError>) -> Void
    ) {
        performRequest(request, retryCount: configuration.retryCount, completion: completion)
    }
    
    // MARK: - Async/Await Support
    @available(iOS 13.0, *)
    func request<T: APIRequest>(_ request: T) async throws -> T.Response {
        return try await withCheckedThrowingContinuation { continuation in
            self.request(request) { result in
                continuation.resume(with: result)
            }
        }
    }
    
    // MARK: - Private Implementation
    private func performRequest<T: APIRequest>(
        _ request: T,
        retryCount: Int,
        completion: @escaping (Result<T.Response, APIError>) -> Void
    ) {
        guard let url = buildURL(for: request) else {
            completion(.failure(.invalidURL))
            return
        }
        
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = request.method.rawValue
        
        // Add headers
        if let headers = request.headers {
            for (key, value) in headers {
                urlRequest.setValue(value, forHTTPHeaderField: key)
            }
        }
        
        // Add parameters
        if let parameters = request.parameters {
            do {
                switch request.method {
                case .GET:
                    // For GET requests, add parameters to URL
                    if var urlComponents = URLComponents(url: url, resolvingAgainstBaseURL: false) {
                        urlComponents.queryItems = parameters.map { URLQueryItem(name: $0.key, value: "\($0.value)") }
                        urlRequest.url = urlComponents.url
                    }
                case .POST, .PUT, .PATCH:
                    // For other requests, add parameters to body
                    urlRequest.httpBody = try JSONSerialization.data(withJSONObject: parameters)
                    urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
                case .DELETE:
                    break
                }
            } catch {
                completion(.failure(.encodingError(error)))
                return
            }
        }
        
        session.dataTask(with: urlRequest) { [weak self] data, response, error in
            if let error = error {
                if retryCount > 0 {
                    self?.performRequest(request, retryCount: retryCount - 1, completion: completion)
                } else {
                    completion(.failure(.networkError(error)))
                }
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse else {
                completion(.failure(.networkError(NSError(domain: "Invalid Response", code: -1))))
                return
            }
            
            // Check HTTP status code
            guard 200...299 ~= httpResponse.statusCode else {
                let errorMessage = data.flatMap { String(data: $0, encoding: .utf8) }
                if retryCount > 0 && httpResponse.statusCode >= 500 {
                    self?.performRequest(request, retryCount: retryCount - 1, completion: completion)
                } else {
                    completion(.failure(.httpError(httpResponse.statusCode, errorMessage)))
                }
                return
            }
            
            guard let data = data else {
                completion(.failure(.noData))
                return
            }
            
            do {
                let decodedResponse = try self?.decoder.decode(T.Response.self, from: data)
                if let response = decodedResponse {
                    completion(.success(response))
                } else {
                    completion(.failure(.decodingError(NSError(domain: "Decoding failed", code: -1))))
                }
            } catch {
                completion(.failure(.decodingError(error)))
            }
        }.resume()
    }
    
    private func buildURL<T: APIRequest>(for request: T) -> URL? {
        guard let baseURL = URL(string: configuration.baseURL) else { return nil }
        return baseURL.appendingPathComponent(request.path)
    }
}

// MARK: - Error Callback Support
extension APIManager {
    typealias ErrorCallback = (APIError) -> Void
    
    func request<T: APIRequest>(
        _ request: T,
        onError: @escaping ErrorCallback,
        completion: @escaping (T.Response) -> Void
    ) {
        self.request(request) { result in
            switch result {
            case .success(let response):
                completion(response)
            case .failure(let error):
                onError(error)
            }
        }
    }
}

// MARK: - Session URL Support
extension APIManager {
    func updateConfiguration(baseURL: String? = nil, timeout: TimeInterval? = nil, retryCount: Int? = nil) -> APIManager {
        let newConfig = APIConfiguration(
            baseURL: baseURL ?? configuration.baseURL,
            timeout: timeout ?? configuration.timeout,
            retryCount: retryCount ?? configuration.retryCount
        )
        return APIManager(configuration: newConfig)
    }
    
    func setSessionHeaders(_ headers: [String: String]) {
        session.configuration.httpAdditionalHeaders = headers
    }
}