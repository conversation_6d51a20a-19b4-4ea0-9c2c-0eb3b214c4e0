//
//  OAuthManager.swift
//  CryptaVault
//
//  Created by <PERSON><PERSON><PERSON> on 2025/8/4.
//

import Foundation
import UIKit

// MARK: - OAuth Token
struct OAuthToken: Codable {
    let accessToken: String
    let refreshToken: String?
    let expiresIn: TimeInterval
    let tokenType: String
    let scope: String?
    
    enum CodingKeys: String, CodingKey {
        case accessToken = "access_token"
        case refreshToken = "refresh_token"
        case expiresIn = "expires_in"
        case tokenType = "token_type"
        case scope
    }
}

// MARK: - OAuth Provider
enum OAuthProvider: String, CaseIterable {
    case google = "google"
    case facebook = "facebook"
    case twitter = "twitter"
    
    var clientId: String {
        switch self {
        case .google:
            return "YOUR_GOOGLE_CLIENT_ID"
        case .facebook:
            return "YOUR_FACEBOOK_CLIENT_ID"
        case .twitter:
            return "YOUR_TWITTER_CLIENT_ID"
        }
    }
    
    var redirectURI: String {
        return "cryptavault://oauth/\(rawValue)"
    }
    
    var authorizationURL: String {
        switch self {
        case .google:
            return "https://accounts.google.com/o/oauth2/v2/auth"
        case .facebook:
            return "https://www.facebook.com/v18.0/dialog/oauth"
        case .twitter:
            return "https://twitter.com/i/oauth2/authorize"
        }
    }
    
    var tokenURL: String {
        switch self {
        case .google:
            return "https://oauth2.googleapis.com/token"
        case .facebook:
            return "https://graph.facebook.com/v18.0/oauth/access_token"
        case .twitter:
            return "https://api.twitter.com/2/oauth2/token"
        }
    }
    
    var scope: String {
        switch self {
        case .google:
            return "openid email profile"
        case .facebook:
            return "email public_profile"
        case .twitter:
            return "tweet.read users.read"
        }
    }
}

// MARK: - OAuth Error
enum OAuthError: Error, LocalizedError {
    case invalidURL
    case authorizationDenied
    case invalidState
    case missingAuthorizationCode
    case tokenExchangeFailed(String)
    case networkError(Error)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid OAuth URL"
        case .authorizationDenied:
            return "User denied authorization"
        case .invalidState:
            return "Invalid OAuth state parameter"
        case .missingAuthorizationCode:
            return "Missing authorization code"
        case .tokenExchangeFailed(let message):
            return "Token exchange failed: \(message)"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        }
    }
}

// MARK: - OAuth Request Models
struct OAuthTokenRequest: APIRequest {
    typealias Response = OAuthToken
    
    let path: String
    let method: HTTPMethod = .POST
    let parameters: [String: Any]?
    let headers: [String: String]?
    
    init(provider: OAuthProvider, authorizationCode: String, codeVerifier: String? = nil) {
        self.path = ""
        
        var params: [String: Any] = [
            "grant_type": "authorization_code",
            "client_id": provider.clientId,
            "redirect_uri": provider.redirectURI,
            "code": authorizationCode
        ]
        
        if let codeVerifier = codeVerifier {
            params["code_verifier"] = codeVerifier
        }
        
        self.parameters = params
        self.headers = ["Content-Type": "application/x-www-form-urlencoded"]
    }
}

// MARK: - OAuth Manager
class OAuthManager {
    static let shared = OAuthManager()
    
    private var currentAuthSession: (provider: OAuthProvider, state: String, completion: (Result<OAuthToken, Error>) -> Void)?
    private let apiManager: APIManager
    
    private init() {
        self.apiManager = APIManager.shared
    }
    
    // MARK: - OAuth Login Methods
    func loginWithGoogle(completion: @escaping (Result<OAuthToken, Error>) -> Void) {
        startOAuthFlow(provider: .google, completion: completion)
    }
    
    func loginWithFacebook(completion: @escaping (Result<OAuthToken, Error>) -> Void) {
        startOAuthFlow(provider: .facebook, completion: completion)
    }
    
    func loginWithTwitter(completion: @escaping (Result<OAuthToken, Error>) -> Void) {
        startOAuthFlow(provider: .twitter, completion: completion)
    }
    
    // MARK: - Logout
    func logout(completion: @escaping (Bool) -> Void) {
        // Clear stored tokens
        UserDefaults.standard.removeObject(forKey: "oauth_token")
        UserDefaults.standard.removeObject(forKey: "oauth_refresh_token")
        UserDefaults.standard.removeObject(forKey: "oauth_provider")
        
        // Clear session
        currentAuthSession = nil
        
        completion(true)
    }
    
    // MARK: - Private Methods
    private func startOAuthFlow(provider: OAuthProvider, completion: @escaping (Result<OAuthToken, Error>) -> Void) {
        let state = generateRandomState()
        currentAuthSession = (provider: provider, state: state, completion: completion)
        
        guard let authURL = buildAuthorizationURL(provider: provider, state: state) else {
            completion(.failure(OAuthError.invalidURL))
            return
        }
        
        // Open OAuth URL in Safari
        DispatchQueue.main.async {
            if UIApplication.shared.canOpenURL(authURL) {
                UIApplication.shared.open(authURL, options: [:]) { success in
                    if !success {
                        completion(.failure(OAuthError.invalidURL))
                    }
                }
            } else {
                completion(.failure(OAuthError.invalidURL))
            }
        }
    }
    
    private func buildAuthorizationURL(provider: OAuthProvider, state: String) -> URL? {
        var components = URLComponents(string: provider.authorizationURL)
        components?.queryItems = [
            URLQueryItem(name: "client_id", value: provider.clientId),
            URLQueryItem(name: "redirect_uri", value: provider.redirectURI),
            URLQueryItem(name: "response_type", value: "code"),
            URLQueryItem(name: "scope", value: provider.scope),
            URLQueryItem(name: "state", value: state)
        ]
        
        return components?.url
    }
    
    private func generateRandomState() -> String {
        let characters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        return String((0..<32).map { _ in characters.randomElement()! })
    }
    
    // MARK: - Handle OAuth Callback
    func handleOAuthCallback(url: URL) {
        guard let session = currentAuthSession else { return }
        
        let components = URLComponents(url: url, resolvingAgainstBaseURL: false)
        
        // Check for error
        if let error = components?.queryItems?.first(where: { $0.name == "error" })?.value {
            session.completion(.failure(OAuthError.authorizationDenied))
            currentAuthSession = nil
            return
        }
        
        // Validate state
        guard let state = components?.queryItems?.first(where: { $0.name == "state" })?.value,
              state == session.state else {
            session.completion(.failure(OAuthError.invalidState))
            currentAuthSession = nil
            return
        }
        
        // Get authorization code
        guard let authorizationCode = components?.queryItems?.first(where: { $0.name == "code" })?.value else {
            session.completion(.failure(OAuthError.missingAuthorizationCode))
            currentAuthSession = nil
            return
        }
        
        // Exchange code for token
        exchangeCodeForToken(provider: session.provider, authorizationCode: authorizationCode) { result in
            session.completion(result)
            self.currentAuthSession = nil
        }
    }
    
    private func exchangeCodeForToken(provider: OAuthProvider, authorizationCode: String, completion: @escaping (Result<OAuthToken, Error>) -> Void) {
        // Create a custom API manager for token endpoint
        let tokenAPIManager = apiManager.updateConfiguration(baseURL: provider.tokenURL)
        
        let request = OAuthTokenRequest(provider: provider, authorizationCode: authorizationCode)
        
        tokenAPIManager.request(request) { result in
            switch result {
            case .success(let token):
                self.storeToken(token, provider: provider)
                completion(.success(token))
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }
    
    private func storeToken(_ token: OAuthToken, provider: OAuthProvider) {
        UserDefaults.standard.set(token.accessToken, forKey: "oauth_token")
        if let refreshToken = token.refreshToken {
            UserDefaults.standard.set(refreshToken, forKey: "oauth_refresh_token")
        }
        UserDefaults.standard.set(provider.rawValue, forKey: "oauth_provider")
    }
    
    // MARK: - Token Management
    func getStoredToken() -> OAuthToken? {
        guard let accessToken = UserDefaults.standard.string(forKey: "oauth_token") else {
            return nil
        }
        
        let refreshToken = UserDefaults.standard.string(forKey: "oauth_refresh_token")
        
        return OAuthToken(
            accessToken: accessToken,
            refreshToken: refreshToken,
            expiresIn: 3600, // Default value
            tokenType: "Bearer",
            scope: nil
        )
    }
    
    func isLoggedIn() -> Bool {
        return getStoredToken() != nil
    }
}