//
//  ViewController.swift
//  CryptaVault
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/25.
//

import UIKit
import SnapKit

struct PasswordEntry {
    let accountName: String
    let website: String
}

class PasswordEntryCell: UITableViewCell {
    private let accountLabel = UILabel()
    private let websiteLabel = UILabel()
    private let containerView = UIView()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        selectionStyle = .none
        backgroundColor = .clear
        
        containerView.backgroundColor = UIColor.systemGray6
        containerView.layer.cornerRadius = 8
        containerView.layer.borderWidth = 1
        containerView.layer.borderColor = UIColor.systemGray4.cgColor
        
        accountLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        accountLabel.textColor = .label
        
        websiteLabel.font = UIFont.systemFont(ofSize: 14)
        websiteLabel.textColor = .secondaryLabel
        
        contentView.addSubview(containerView)
        containerView.addSubview(accountLabel)
        containerView.addSubview(websiteLabel)
        
        containerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(4)
            make.leading.equalToSuperview().offset(16)
            make.trailing.equalToSuperview().offset(-16)
            make.bottom.equalToSuperview().offset(-4)
            make.height.equalTo(60)
        }
        
        accountLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.leading.equalToSuperview().offset(12)
            make.trailing.equalToSuperview().offset(-12)
        }
        
        websiteLabel.snp.makeConstraints { make in
            make.top.equalTo(accountLabel.snp.bottom).offset(4)
            make.leading.equalToSuperview().offset(12)
            make.trailing.equalToSuperview().offset(-12)
            make.bottom.equalToSuperview().offset(-8)
        }
    }
    
    func configure(with entry: PasswordEntry) {
        accountLabel.text = "账户: \(entry.accountName)"
        websiteLabel.text = "站点: \(entry.website)"
    }
}

class ViewController: UIViewController {
    private let searchBar = UISearchBar()
    private let tableView = UITableView()
    private let addButton = UIButton(type: .custom)
    
    private var passwordEntries: [PasswordEntry] = [
        PasswordEntry(accountName: "headwindx", website: "www.google.com"),
        PasswordEntry(accountName: "headwindx", website: "www.google.com"),
        PasswordEntry(accountName: "headwindx", website: "www.google.com"),
        PasswordEntry(accountName: "headwindx", website: "www.google.com"),
        PasswordEntry(accountName: "headwindx", website: "www.google.com")
    ]
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    private func setupUI() {
        view.backgroundColor = .systemBackground
        
        searchBar.placeholder = "搜索"
        searchBar.searchBarStyle = .minimal
        searchBar.layer.cornerRadius = 8
        searchBar.layer.borderWidth = 1
        searchBar.layer.borderColor = UIColor.systemGray4.cgColor
        
        tableView.dataSource = self
        tableView.delegate = self
        tableView.register(PasswordEntryCell.self, forCellReuseIdentifier: "PasswordEntryCell")
        tableView.backgroundColor = .clear
        tableView.separatorStyle = .none
        tableView.showsVerticalScrollIndicator = false
        
        addButton.backgroundColor = UIColor.systemPink
        addButton.setTitle("+", for: .normal)
        addButton.setTitleColor(.white, for: .normal)
        addButton.titleLabel?.font = UIFont.systemFont(ofSize: 24, weight: .medium)
        addButton.layer.cornerRadius = 28
        addButton.layer.shadowColor = UIColor.black.cgColor
        addButton.layer.shadowOffset = CGSize(width: 0, height: 2)
        addButton.layer.shadowRadius = 4
        addButton.layer.shadowOpacity = 0.3
        
        view.addSubview(searchBar)
        view.addSubview(tableView)
        view.addSubview(addButton)
        
        searchBar.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide).offset(16)
            make.leading.equalToSuperview().offset(16)
            make.trailing.equalToSuperview().offset(-16)
            make.height.equalTo(44)
        }
        
        tableView.snp.makeConstraints { make in
            make.top.equalTo(searchBar.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalTo(view.safeAreaLayoutGuide)
        }
        
        addButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-24)
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-24)
            make.width.height.equalTo(56)
        }
    }
    
}

extension ViewController: UITableViewDataSource, UITableViewDelegate {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return passwordEntries.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "PasswordEntryCell", for: indexPath) as! PasswordEntryCell
        cell.configure(with: passwordEntries[indexPath.row])
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 68
    }
}

