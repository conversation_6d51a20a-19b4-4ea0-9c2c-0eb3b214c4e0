//
//  AccountViewController.swift
//  CryptaVault
//
//  Created by <PERSON><PERSON><PERSON> on 2025/8/4.
//

import UIKit
import SnapKit

class AccountViewController: UIViewController {
    private let titleLabel = UILabel()
    private let emailLabel = UILabel()
    private let emailTextField = UITextField()
    private let passwordLabel = UILabel()
    private let passwordTextField = UITextField()
    private let logoutButton = UIButton(type: .system)
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadUserData()
    }
    
    private func setupUI() {
        view.backgroundColor = .systemBackground
        
        titleLabel.text = "账户"
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        titleLabel.textAlignment = .center
        
        setupLabels()
        setupTextFields()
        setupLogoutButton()
        
        addSubviews()
        setupConstraints()
    }
    
    private func setupLabels() {
        emailLabel.text = "邮箱:"
        emailLabel.font = UIFont.systemFont(ofSize: 16)
        emailLabel.textColor = .label
        
        passwordLabel.text = "修改密码:"
        passwordLabel.font = UIFont.systemFont(ofSize: 16)
        passwordLabel.textColor = .label
    }
    
    private func setupTextFields() {
        // Email field (read-only)
        emailTextField.text = "<EMAIL>"
        emailTextField.font = UIFont.systemFont(ofSize: 16)
        emailTextField.backgroundColor = .systemBackground
        emailTextField.layer.cornerRadius = 8
        emailTextField.layer.borderWidth = 1
        emailTextField.layer.borderColor = UIColor.systemGray4.cgColor
        emailTextField.isUserInteractionEnabled = false
        
        let emailPaddingView = UIView(frame: CGRect(x: 0, y: 0, width: 12, height: 44))
        emailTextField.leftView = emailPaddingView
        emailTextField.leftViewMode = .always
        emailTextField.rightView = emailPaddingView
        emailTextField.rightViewMode = .always
        
        // Password field (display only)
        passwordTextField.text = "••••••••"
        passwordTextField.font = UIFont.systemFont(ofSize: 16)
        passwordTextField.backgroundColor = .systemBackground
        passwordTextField.layer.cornerRadius = 8
        passwordTextField.layer.borderWidth = 1
        passwordTextField.layer.borderColor = UIColor.systemGray4.cgColor
        passwordTextField.isUserInteractionEnabled = false
        
        let passwordPaddingView = UIView(frame: CGRect(x: 0, y: 0, width: 12, height: 44))
        passwordTextField.leftView = passwordPaddingView
        passwordTextField.leftViewMode = .always
        passwordTextField.rightView = passwordPaddingView
        passwordTextField.rightViewMode = .always
    }
    
    private func setupLogoutButton() {
        logoutButton.setTitle("退出登录", for: .normal)
        logoutButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        logoutButton.backgroundColor = UIColor.systemRed
        logoutButton.setTitleColor(.white, for: .normal)
        logoutButton.layer.cornerRadius = 8
        logoutButton.addTarget(self, action: #selector(logoutTapped), for: .touchUpInside)
    }
    
    private func addSubviews() {
        view.addSubview(titleLabel)
        view.addSubview(emailLabel)
        view.addSubview(emailTextField)
        view.addSubview(passwordLabel)
        view.addSubview(passwordTextField)
        view.addSubview(logoutButton)
    }
    
    private func setupConstraints() {
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide).offset(20)
            make.centerX.equalToSuperview()
        }
        
        emailLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(40)
            make.leading.equalToSuperview().offset(20)
        }
        
        emailTextField.snp.makeConstraints { make in
            make.top.equalTo(emailLabel.snp.bottom).offset(8)
            make.leading.equalToSuperview().offset(20)
            make.trailing.equalToSuperview().offset(-20)
            make.height.equalTo(44)
        }
        
        passwordLabel.snp.makeConstraints { make in
            make.top.equalTo(emailTextField.snp.bottom).offset(20)
            make.leading.equalToSuperview().offset(20)
        }
        
        passwordTextField.snp.makeConstraints { make in
            make.top.equalTo(passwordLabel.snp.bottom).offset(8)
            make.leading.equalToSuperview().offset(20)
            make.trailing.equalToSuperview().offset(-20)
            make.height.equalTo(44)
        }
        
        logoutButton.snp.makeConstraints { make in
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-30)
            make.leading.equalToSuperview().offset(20)
            make.trailing.equalToSuperview().offset(-20)
            make.height.equalTo(44)
        }
    }
    
    private func loadUserData() {
        // Load user data from OAuth or API
        if let token = OAuthManager.shared.getStoredToken() {
            // In a real app, you would fetch user profile from API
            // For now, we'll use mock data
            loadUserProfile()
        }
    }
    
    private func loadUserProfile() {
        // Mock user profile loading
        // In production, this would call the API
        let apiService = VaultAPIService()
        
        apiService.fetchUserProfile { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let profile):
                    self?.emailTextField.text = profile.email
                case .failure(let error):
                    print("Failed to load user profile: \(error.localizedDescription)")
                    // Keep default email for demo
                }
            }
        }
    }
    
    @objc private func logoutTapped() {
        let alert = UIAlertController(
            title: "退出登录",
            message: "确定要退出登录吗？",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        alert.addAction(UIAlertAction(title: "退出", style: .destructive) { [weak self] _ in
            self?.performLogout()
        })
        
        present(alert, animated: true)
    }
    
    private func performLogout() {
        OAuthManager.shared.logout { [weak self] success in
            DispatchQueue.main.async {
                if success {
                    // Navigate back to login screen
                    if let sceneDelegate = self?.view.window?.windowScene?.delegate as? SceneDelegate {
                        sceneDelegate.window?.rootViewController = LoginViewController()
                    }
                } else {
                    // Show error message
                    let errorAlert = UIAlertController(
                        title: "退出失败",
                        message: "退出登录时发生错误，请重试",
                        preferredStyle: .alert
                    )
                    errorAlert.addAction(UIAlertAction(title: "确定", style: .default))
                    self?.present(errorAlert, animated: true)
                }
            }
        }
    }
}