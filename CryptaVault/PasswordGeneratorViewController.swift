//
//  PasswordGeneratorViewController.swift
//  CryptaVault
//
//  Created by <PERSON><PERSON><PERSON> on 2025/8/4.
//

import UIKit
import SnapKit

class PasswordGeneratorViewController: UIViewController {
    private let titleLabel = UILabel()
    private let passwordDisplayView = UIView()
    private let passwordLabel = UILabel()
    private let lengthSlider = UISlider()
    private let lengthValueLabel = UILabel()
    private let lengthDescriptionLabel = UILabel()
    
    private let numbersCheckbox = UIButton(type: .custom)
    private let numbersLabel = UILabel()
    private let lettersCheckbox = UIButton(type: .custom)
    private let lettersLabel = UILabel()
    private let specialCheckbox = UIButton(type: .custom)
    private let specialLabel = UILabel()
    
    private let generateButton = UIButton(type: .system)
    private let copyButton = UIButton(type: .system)
    
    private var includeNumbers = true
    private var includeLetters = true
    private var includeSpecialChars = true
    private var passwordLength = 8
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        generatePassword()
    }
    
    private func setupUI() {
        view.backgroundColor = .systemBackground
        
        titleLabel.text = "密码机"
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        titleLabel.textAlignment = .center
        
        passwordDisplayView.backgroundColor = UIColor.systemGray6
        passwordDisplayView.layer.cornerRadius = 8
        passwordDisplayView.layer.borderWidth = 1
        passwordDisplayView.layer.borderColor = UIColor.systemGray4.cgColor
        
        passwordLabel.text = "生成的密码"
        passwordLabel.font = UIFont.systemFont(ofSize: 16)
        passwordLabel.textAlignment = .center
        passwordLabel.textColor = .secondaryLabel
        
        lengthSlider.minimumValue = 4
        lengthSlider.maximumValue = 32
        lengthSlider.value = Float(passwordLength)
        lengthSlider.addTarget(self, action: #selector(sliderValueChanged), for: .valueChanged)
        
        lengthValueLabel.text = "\(passwordLength)"
        lengthValueLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        lengthValueLabel.textAlignment = .center
        lengthValueLabel.backgroundColor = .systemBackground
        lengthValueLabel.layer.cornerRadius = 4
        lengthValueLabel.layer.borderWidth = 1
        lengthValueLabel.layer.borderColor = UIColor.systemGray4.cgColor
        
        lengthDescriptionLabel.text = "位密码"
        lengthDescriptionLabel.font = UIFont.systemFont(ofSize: 16)
        lengthDescriptionLabel.textColor = .label
        
        setupCheckbox(numbersCheckbox, label: numbersLabel, text: "数字", isSelected: includeNumbers)
        setupCheckbox(lettersCheckbox, label: lettersLabel, text: "大小写字母", isSelected: includeLetters)
        setupCheckbox(specialCheckbox, label: specialLabel, text: "特殊符号", isSelected: includeSpecialChars)
        
        generateButton.setTitle("生成", for: .normal)
        generateButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        generateButton.backgroundColor = .systemBackground
        generateButton.setTitleColor(.label, for: .normal)
        generateButton.layer.cornerRadius = 8
        generateButton.layer.borderWidth = 1
        generateButton.layer.borderColor = UIColor.systemGray4.cgColor
        generateButton.addTarget(self, action: #selector(generatePasswordTapped), for: .touchUpInside)
        
        copyButton.setTitle("复制", for: .normal)
        copyButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        copyButton.backgroundColor = .systemBackground
        copyButton.setTitleColor(.label, for: .normal)
        copyButton.layer.cornerRadius = 8
        copyButton.layer.borderWidth = 1
        copyButton.layer.borderColor = UIColor.systemGray4.cgColor
        copyButton.addTarget(self, action: #selector(copyPasswordTapped), for: .touchUpInside)
        
        addSubviews()
        setupConstraints()
    }
    
    private func setupCheckbox(_ checkbox: UIButton, label: UILabel, text: String, isSelected: Bool) {
        checkbox.setImage(UIImage(systemName: isSelected ? "checkmark.square.fill" : "square"), for: .normal)
        checkbox.tintColor = .systemBlue
        checkbox.addTarget(self, action: #selector(checkboxTapped(_:)), for: .touchUpInside)
        
        label.text = text
        label.font = UIFont.systemFont(ofSize: 16)
        label.textColor = .label
    }
    
    private func addSubviews() {
        view.addSubview(titleLabel)
        view.addSubview(passwordDisplayView)
        passwordDisplayView.addSubview(passwordLabel)
        view.addSubview(lengthSlider)
        view.addSubview(lengthValueLabel)
        view.addSubview(lengthDescriptionLabel)
        
        view.addSubview(numbersCheckbox)
        view.addSubview(numbersLabel)
        view.addSubview(lettersCheckbox)
        view.addSubview(lettersLabel)
        view.addSubview(specialCheckbox)
        view.addSubview(specialLabel)
        
        view.addSubview(generateButton)
        view.addSubview(copyButton)
    }
    
    private func setupConstraints() {
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide).offset(20)
            make.centerX.equalToSuperview()
        }
        
        passwordDisplayView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(30)
            make.leading.equalToSuperview().offset(20)
            make.trailing.equalToSuperview().offset(-20)
            make.height.equalTo(50)
        }
        
        passwordLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.equalToSuperview().offset(12)
            make.trailing.equalToSuperview().offset(-12)
        }
        
        lengthSlider.snp.makeConstraints { make in
            make.top.equalTo(passwordDisplayView.snp.bottom).offset(40)
            make.leading.equalToSuperview().offset(20)
            make.trailing.equalTo(lengthValueLabel.snp.leading).offset(-20)
        }
        
        lengthValueLabel.snp.makeConstraints { make in
            make.centerY.equalTo(lengthSlider)
            make.trailing.equalTo(lengthDescriptionLabel.snp.leading).offset(-8)
            make.width.equalTo(40)
            make.height.equalTo(30)
        }
        
        lengthDescriptionLabel.snp.makeConstraints { make in
            make.centerY.equalTo(lengthSlider)
            make.trailing.equalToSuperview().offset(-20)
        }
        
        numbersCheckbox.snp.makeConstraints { make in
            make.top.equalTo(lengthSlider.snp.bottom).offset(40)
            make.leading.equalToSuperview().offset(20)
            make.width.height.equalTo(24)
        }
        
        numbersLabel.snp.makeConstraints { make in
            make.centerY.equalTo(numbersCheckbox)
            make.leading.equalTo(numbersCheckbox.snp.trailing).offset(12)
        }
        
        lettersCheckbox.snp.makeConstraints { make in
            make.top.equalTo(numbersCheckbox.snp.bottom).offset(20)
            make.leading.equalToSuperview().offset(20)
            make.width.height.equalTo(24)
        }
        
        lettersLabel.snp.makeConstraints { make in
            make.centerY.equalTo(lettersCheckbox)
            make.leading.equalTo(lettersCheckbox.snp.trailing).offset(12)
        }
        
        specialCheckbox.snp.makeConstraints { make in
            make.top.equalTo(lettersCheckbox.snp.bottom).offset(20)
            make.leading.equalToSuperview().offset(20)
            make.width.height.equalTo(24)
        }
        
        specialLabel.snp.makeConstraints { make in
            make.centerY.equalTo(specialCheckbox)
            make.leading.equalTo(specialCheckbox.snp.trailing).offset(12)
        }
        
        generateButton.snp.makeConstraints { make in
            make.top.equalTo(specialCheckbox.snp.bottom).offset(40)
            make.centerX.equalToSuperview()
            make.width.equalTo(100)
            make.height.equalTo(40)
        }
        
        copyButton.snp.makeConstraints { make in
            make.top.equalTo(generateButton.snp.bottom).offset(16)
            make.centerX.equalToSuperview()
            make.width.equalTo(100)
            make.height.equalTo(40)
        }
    }
    
    
    @objc private func sliderValueChanged(_ sender: UISlider) {
        passwordLength = Int(sender.value)
        lengthValueLabel.text = "\(passwordLength)"
        generatePassword()
    }
    
    @objc private func generatePasswordTapped() {
        generatePassword()
    }
    
    @objc private func checkboxTapped(_ sender: UIButton) {
        switch sender {
        case numbersCheckbox:
            includeNumbers.toggle()
            updateCheckbox(numbersCheckbox, isSelected: includeNumbers)
        case lettersCheckbox:
            includeLetters.toggle()
            updateCheckbox(lettersCheckbox, isSelected: includeLetters)
        case specialCheckbox:
            includeSpecialChars.toggle()
            updateCheckbox(specialCheckbox, isSelected: includeSpecialChars)
        default:
            break
        }
        generatePassword()
    }
    
    private func updateCheckbox(_ checkbox: UIButton, isSelected: Bool) {
        checkbox.setImage(UIImage(systemName: isSelected ? "checkmark.square.fill" : "square"), for: .normal)
    }
    
    @objc private func copyPasswordTapped() {
        if passwordLabel.text != "生成的密码" {
            UIPasteboard.general.string = passwordLabel.text
            
            let alert = UIAlertController(title: "已复制", message: "密码已复制到剪贴板", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
        }
    }
    
    private func generatePassword() {
        guard includeNumbers || includeLetters || includeSpecialChars else {
            passwordLabel.text = "请选择至少一种字符类型"
            return
        }
        
        var characterSet = ""
        
        if includeNumbers {
            characterSet += "0123456789"
        }
        
        if includeLetters {
            characterSet += "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
        }
        
        if includeSpecialChars {
            characterSet += "!@#$%^&*()_+-=[]{}|;:,.<>?"
        }
        
        var password = ""
        for _ in 0..<passwordLength {
            let randomIndex = Int.random(in: 0..<characterSet.count)
            let character = characterSet[characterSet.index(characterSet.startIndex, offsetBy: randomIndex)]
            password += String(character)
        }
        
        passwordLabel.text = password
        passwordLabel.font = UIFont.monospacedSystemFont(ofSize: 14, weight: .medium)
        passwordLabel.textColor = .label
    }
}